<template>
  <el-dialog
    width="900px"
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-button type="primary" size="small" @click="AddPriceRef.open(customerClassId)">新增定价</el-button>
    <el-button type="danger" size="small" @click="del">删除选中</el-button>
    <CommonTable
      class="mt10"
      heightType="fixed"
      height="400px"
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
      :options="{ isSelection: true }"
      v-model:selectData="selectData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
  <AddPrice ref="AddPriceRef" @success="getList" />
</template>

<script setup>
import { ref } from "vue";
import CommonTable from '@/components/CommonTable.vue';
import Pagination from "@/components/Pagination.vue";
import AddPrice from './addPrice.vue';
import Api from '@/api/user/index.js';
import { useList } from "@/hooks/useCommonTable.js";
import { Confirm } from '@/utils/global.js';
import { ElNotification } from 'element-plus';

const { tableData, pageParams, searchParams, getList, listReset } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdCustomerClassPrice_priceList(query)
    return {list:records,total}
  },
})

const dialogVisible = ref(false)
const title = ref('商品列表')
const customerClassId = ref(0)
const open = (id) => {
  if (id) {
    customerClassId.value = id
    searchParams.value.customerClassId = id
    getList()
  }
  dialogVisible.value = true
}

const close = () => {
  listReset()
  selectData.value = []
}

defineExpose({
  open
})

const selectData = ref([])
const del = () => {
  Confirm(() => {
    if (selectData.value && selectData.value.length > 0) {
      Api.bdCustomerClassPrice_batchDel({
        ids: selectData.value.map(item => item.id)
      }).then(res => {
        selectData.value = []
        getList()
      })
    } else {
      ElNotification.warning('请勾选数据')
    }
  }, '是否删除选中数据？')
}

const AddPriceRef = ref(null)
const handleData = {
  width: '100',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        AddPriceRef.value.open(customerClassId.value, row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'name',
    label: '商品名称',
    width: ''
  },
  {
    prop: 'unitPrice',
    label: '定价',
    width: ''
  },
  {
    prop: 'brand',
    label: '品牌',
    width: ''
  },
  {
    prop: 'deliveryDate',
    label: '货期',
    width: ''
  },
  {
    prop: 'facePrice',
    label: '面价',
    width: ''
  },
  {
    prop: 'model',
    label: '型号',
    width: ''
  },
  {
    prop: 'orderNumber',
    label: '订货号',
    width: ''
  },
  {
    prop: 'unit',
    label: '计量单位',
    width: ''
  },
  {
    prop: 'minBuyNum',
    label: '最小起购量',
    width: ''
  },
]
</script>
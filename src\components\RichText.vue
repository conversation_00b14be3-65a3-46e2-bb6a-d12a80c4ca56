<template>
  <div>
    <editor id="tinymce" v-model="tinyValue" :init="init"></editor>
  </div>
</template>

<script>
/**
 * 此组件源代码太大，直接本地部署体验很差
 * 需要在index.html中引入相关cdn资源，如资源失效请自行更换链接
 * 语言包和css相关文件需要放在public目录下
 */
import oss from '@/api/oss/oss.js'
import Editor from '@tinymce/tinymce-vue'
export default {
  name: 'RichText',
  components: {
    'editor': Editor
  },
  props: {
    modelValue: {
      type: String
    },
    uploadPath: {
      type: String,
      require: true
    }
  },

  computed: {
    tinyValue: {
      get () {
        return this.modelValue
      },
      set (val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  data () {
    return {
      init: {
        language_url: '/tinymce/langs/zh_CN.js', // 语言包的路径，需要在public下
        language: 'zh_CN', // 语言
        skin_url: '/tinymce/skins/ui/oxide', // skin路径，需要在public下
        height: 400, // 编辑器高度
        branding: false, // 是否禁用“Powered by TinyMCE”
        menubar: true, // 顶部菜单栏显示
        plugins: 'lists image table wordcount code link hr preview searchreplace',
        toolbar: 'undo redo | formatselect | bold italic hr forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image table | code searchreplace link preview | removeformat ',
        images_upload_handler: (blobInfo, succFun, failFun) => {
          let file = blobInfo.blob() // 转化为易于理解的file对象
          // 文件验证 type size
          // if (!this.checkFile(file, [], 2)) return
          oss.uploadFile(file).then(res => {
            succFun(res.url)
          }).catch(err => {
            failFun(err.message)
          })
        }
      }
    }
  },
  methods: {
  }
}
</script>

<style lang="scss">
  /*在el-dialog中弹窗会被覆盖，加大z-index解决*/
  .tox-silver-sink{
    z-index: 2500 !important;
  }
</style>

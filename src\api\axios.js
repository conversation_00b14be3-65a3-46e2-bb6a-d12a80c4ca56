import { toRaw } from "vue";
import Axios from "axios";
import { ElNotification } from "element-plus";
import { loadingShow, tryHideLoading } from "./loading";
import store from "../store";
import router from "../router";

const baseURL = "https://yun.jiqinyun.com/internalSystemAPI";

const axios = Axios.create({
  // baseURL,
  timeout: 60000, // 请求超时 20s
});

axios.interceptors.request.use(
  (response) => {
    
    if (!response.data) response.data = {};

    if (!response.data.noLoading) {
      loadingShow();
    }

    const userInfo = toRaw(store.getters.userInfo);
    if (userInfo.token) {
      response.headers["token"] = userInfo.token;
    }

    return response;
  },
  (error) => {
    tryHideLoading();
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    tryHideLoading();
    if (!response.status || response.status !== 200)
      return ElNotification.error("接口返回识别码异常,请联系管理员");
    let res = response.data;
    switch (res.code) {
      case 1:
        if (res.data && res.data.hasOwnProperty("total")) {
          return res.data;
        }
        if (!res.data && res.msg) {
          ElNotification.success(res.msg);
        }
        return res;
      case 0:
        ElNotification.error(res.msg);
        return Promise.reject(res);
      case -1:
        ElNotification.error(res.msg);
        return Promise.reject(res);
      case -2:
        ElNotification.error("身份信息失效，请重新登录");
        store.commit("SET_USER_INFO", {}); // 清空登录信息
        router.replace("/login");
        return Promise.reject(res);
      case -99:
        ElNotification.error("身份信息失效，请重新登录");
        store.commit("SET_USER_INFO", {}); // 清空登录信息
        router.replace("/login");
        return Promise.reject(res);
      default:
        return res;
    }
  },
  (error) => {
    tryHideLoading();
    if (error.response && error.response.data) {
      const code = error.response.status;
      const msg = error.response.data.message;
      ElNotification.error({
        title: `错误码:${code}`,
        message: msg,
      });
      console.error(`[Axios Error]`, error.response);
    } else {
      ElNotification.error(error);
    }
    return Promise.reject(error);
  }
);

export default axios;

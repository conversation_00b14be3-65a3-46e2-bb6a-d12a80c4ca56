<template>
  <div class="table-container">
    <el-table
      :data="tableData"
      border
      :height="['fill', 'fixed'].includes(heightType) ? height : null"
      :header-cell-style="{
        background: '#f5f5f5',
        color: '#3D454D',
      }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="options.isSelection"
        type="selection"
        width="50"
        align="center"
      ></el-table-column>
      <el-table-column v-if="options.isExpand" type="expand" fixed="left">
        <template #default="{ row }">
          <slot name="expand" :row="row"></slot>
        </template>
      </el-table-column>
      <template v-for="(item, index) in tableHeader">
        <el-table-column
          :key="index"
          show-overflow-tooltip
          v-if="item.prop && !item.type"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :align="item.align || 'center'"
          min-width="120px"
        >
          <template #header v-if="item.headerSlot">
            <slot :name="item.headerSlot"></slot>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.slot"
          :key="index"
          show-overflow-tooltip
          :label="item.label"
          :width="item.width"
          :fixed="item.fixed || false"
          :align="item.align || 'center'"
          min-width="120px"
        >
          <template #default="{ row }">
            <slot :name="item.slot" :row="row"></slot>
          </template>
        </el-table-column>
        <el-table-column
          :key="index"
          v-if="item.type === 'input'"
          :label="item.label"
          :width="item.width"
          min-width="120px"
        >
          <template #default="{ row }">
            <el-input v-model="row[item.prop]" />
          </template>
        </el-table-column>
        <el-table-column
          :key="index"
          v-if="item.type === 'select'"
          :label="item.label"
          :width="item.width"
          min-width="120px"
        >
          <template #default="{ row }">
            <el-select
              v-model="row[item.prop]"
              :disabled="row.disabled ? row.disabled(row) : false"
            >
              <el-option
                v-for="(option, index) in item.options"
                :key="index"
                :label="option.label"
                :value="option.value"
              ></el-option>
              <el-option
                v-for="option in selectDatas[item.prop]"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="handleData && handleData.handle && handleData.handle.length > 0"
        fixed="right"
        align="center"
        label="操作"
        :width="handleData.width"
      >
        <template #default="{ row, $index }">
          <div class="flex-cc">
            <el-button
              v-for="(item, index) in handleData.handle"
              :key="index"
              text
              bg
              type="primary"
              size="small"
              @click="item.fun(row, $index)"
              >{{ item.label }}</el-button
            >
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty
          v-if="heightType === 'fill'"
          :image-size="120"
          description="暂无数据"
        ></el-empty>
        <span v-else>暂无数据</span>
      </template>
    </el-table>
  </div>
</template>

<script setup>
import { toRaw, reactive } from "vue";

const props = defineProps({
  /**
   * fill 高度跟随容器100%,主要用于flex布局下的自适应高度
   * auto 高度取决于表格内容
   * fixed 固定高度，不足时有滚动条，需配合height使用
   */
  heightType: {
    type: String,
    default: "fill",
    validator: (value) => {
      return ["fill", "auto", "fixed"].includes(value);
    },
  },
  height: {
    type: String,
    default: "100%",
  },
  tableHeader: {
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  options: {
    type: Object,
    default: () => {
      return { isExpand: false, isSelection: false };
    },
  },
  handleData: {
    type: Object,
    default: () => {
      return {
        handle: [],
      };
    },
  },
});

const emit = defineEmits(["update:selectData"]);

const handleSelectionChange = (val) => {
  let originValue = val.map((item) => toRaw(item));
  emit("update:selectData", originValue);
};

const selectDatas = reactive({});
// onMounted(() => {
//   let selectApis = props.tableHeader.filter((item) => item.selectApi);
//   if (selectApis.length > 0) {
//     for (let item of selectApis) {
//       let params = item.selectApiParams || {
//         pageNum: 1,
//         pageSize: 10000,
//       };
//       API[item.selectApi](params).then((res) => {
//         selectDatas[item.prop] = res.records;
//       });
//     }
//   }
// });
</script>

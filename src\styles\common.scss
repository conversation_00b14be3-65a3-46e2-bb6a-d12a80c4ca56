@import './mixin.scss';

*{
	box-sizing: border-box;
}

.flex-cc{
  @include flex(row,center,center)
}

.flex-c{
  @include flex(row,flex-start,center)
}

.flex-bc{
  @include flex(row,space-between,center)
}

.flex-col-cc{
  @include flex(column,center,center)
}

.flex-col-bc{
  @include flex(column,space-between,center)
}

.flex-1{
  flex: 1;
}

.flex-1-roll{
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

@for $i from 1 through 12 {
	.mt#{5 * $i} {
		margin-top: (5px * $i);
	}

	.ml#{5 * $i} {
		margin-left: (5px * $i);
	}

	.mr#{5 * $i} {
		margin-right: (5px * $i);
	}

	.mlr#{5 * $i} {
		margin-left : (5px * $i);
		margin-right: (5px * $i);
	}

	.mb#{5 * $i} {
		margin-bottom: (5px * $i);
	}
}

@for $i from 6 through 25 {
	.fz#{2 * $i} {
		font-size: (2px * $i);
	}
}

.w100{
  width: 100%;
}

.h100{
  height: 100%;
}

.c333{
  color: #333;
}

.c666{
  color: #666;
}

.c999{
  color: #999;
}

.fwb{
	font-weight: bold;
}

.tac{
  text-align: center;
}

.btn-hover{
	cursor: pointer;
	transition: opacity .3s ease;
	&:hover{
		opacity: .8;
	}
}

.link{
	cursor: pointer;
	transition: opacity .3s ease;
	color: $primary;
	&:hover{
		opacity: .8;
	}
}

.table-container{
	width: 100%;
	flex: 1;
  overflow: hidden;
	.el-table {
    .el-table__body-wrapper {
      overflow: auto !important;
    }
  }
}

// 产品表格样式
.detail-table{
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  color: #666;
  thead{
    th{
      min-width: 60px;
      background-color: #ebf4f5;
    }
  }
  td,th{
    border: 1px solid #cad9ea;
    text-align: center;
    padding: 8px 0;
  }
}

.status{
	border: 1px solid #ccc;
	padding: 0 2px;
	border-radius: 2px;
	margin-right: 2px;
	&.red{
		color: $danger;
		border-color: $danger;
	}
	&.green{
		color: $success;
		border-color: $success;
	}
}

.label-list {
  display: flex;
  flex-wrap: wrap;
  line-height: 1.5;
  &.col-3 {
    .label-item {
      flex: 0 0 33.33%;
    }
  }
  .label-item {
    flex: 0 0 50%;
    font-size: 12px;
    display: flex;
    align-items: center;
    &.auto {
      width: 100%;
      flex: 0 0 auto;
    }
    .label-title {
      color: #999;
      flex: 0 0 70px;
      padding: 5px 10px 5px 0;
    }
  }
}

.label-list-half {
  display: flex;
  flex-wrap: wrap;
  line-height: 1.5;
  .label-item {
    flex: 0 0 50%;
    font-size: 12px;
    display: flex;
    align-items: center;
    &.auto {
      width: 50%;
      flex: 0 0 auto;
    }
    .label-title {
      color: #999;
      flex: 0 0 70px;
      padding: 5px 10px 5px 0;
    }
  }
}

.common-info-bg{
  margin-top: 10px;
  background-color: #f5f6fa;
  padding: 10px;
  .common-info-title{
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}

.fixed-view-container{
	width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.view-title.border{
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
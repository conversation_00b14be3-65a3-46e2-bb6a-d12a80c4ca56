<template>
  <div class="fixed-view-container">
    <ViewHeader title="基础设置" add="保存" @add="submit" />
    <div class="flex-1-roll">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <InfoPanel title="商城基础信息">
              <el-form-item label="商城名称" prop="name">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
              <el-form-item label="默认业务员" prop="defSaleId">
                <el-select v-model="form.defSaleId" style="width: 100%">
                  <el-option v-for="item in selectData.salesUsers" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="包邮门槛金额" prop="freeShippingCondition">
                <el-input v-model="form.freeShippingCondition"></el-input>
              </el-form-item>
              <el-form-item label="运费" prop="freightAmount">
                <el-input v-model="form.freightAmount"></el-input>
              </el-form-item> -->
              <el-form-item label="咨询电话" prop="informationHotline">
                <el-input v-model="form.informationHotline"></el-input>
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="form.email"></el-input>
              </el-form-item>
              <el-form-item label="logo图片" prop="logo">
                <ImgUpload v-model="form.logo" :size="0.5" text="建议尺寸100x100,透明" />
              </el-form-item>
              <el-form-item label="icon图片" prop="icon">
                <ImgUpload v-model="form.icon" :size="0.1" text="建议尺寸32x32,透明" />
              </el-form-item>
            </InfoPanel>
          </el-col>
          <el-col :span="12">
            <InfoPanel title="开票信息">
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="form.companyName"></el-input>
              </el-form-item>
              <el-form-item label="银行账户" prop="bankAccount">
                <el-input v-model="form.bankAccount"></el-input>
              </el-form-item>
              <el-form-item label="开户行" prop="bankAddress">
                <el-input v-model="form.bankAddress"></el-input>
              </el-form-item>
              <el-form-item label="开票地址" prop="billAddress">
                <el-input v-model="form.billAddress"></el-input>
              </el-form-item>
              <el-form-item label="开票电话" prop="billPhone">
                <el-input v-model="form.billPhone"></el-input>
              </el-form-item>
              <el-form-item label="税号" prop="taxNumber">
                <el-input v-model="form.taxNumber"></el-input>
              </el-form-item>
            </InfoPanel>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: '/set/base_set'
}
</script>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import ImgUpload from '@/components/ImgUpload.vue';
import InfoPanel from '@/components/InfoPanel.vue';
import Api from '@/api/set/index.js';
import UserApi from '@/api/login/index.js';

const form = ref({
  name: "",
  logo: "",
  icon: "",
  defSaleId: null,
  freeShippingCondition: '',
  freightAmount: '',
  informationHotline: '',
  email: '',

  bankAccount: '',
  bankAddress: '',
  billAddress: '',
  billPhone: '',
  companyName: '',
  taxNumber: '',
})
const rules = {
  name: [{ required: true, message: '请填写系统名称', trigger: 'blur', }],
  logo: [{ required: false, message: '请上传图片', trigger: 'change', }],
  icon: [{ required: false, message: '请上传图片', trigger: 'change', }],
  defSaleId: [{ required: true, message: '请选择销售员', trigger: 'change', }],
  freeShippingCondition: [{ required: true, message: '请填写包邮门槛金额', trigger: 'blur', }],
  freightAmount: [{ required: true, message: '请填写运费', trigger: 'blur', }],
  informationHotline: [{ required: true, message: '请填写咨询电话', trigger: 'blur', }],

  bankAccount: [{ required: true, message: '请填写银行账户', trigger: 'blur', }],
  bankAddress: [{ required: true, message: '请填写开户行', trigger: 'blur', }],
  billAddress: [{ required: true, message: '请填写开票地址', trigger: 'blur', }],
  billPhone: [{ required: true, message: '请填写开票电话', trigger: 'blur', }],
  companyName: [{ required: true, message: '请填写公司名称', trigger: 'blur', }],
  taxNumber: [{ required: true, message: '请填写税号', trigger: 'blur', }],
}
const formRef = ref(null)
const submit = () => {
  formRef.value.validate(valid => {
    if (valid) {
      Api.companyInfo_save(form.value).then(res => {
        getDetail()
      })
    }
  })
}

const getDetail = () => {
  Api.companyInfo_info().then(res => {
    form.value = res.data
  })
}

const selectData = reactive({
  salesUsers: [],
})
const getSelectData = () => {
  UserApi.userList({
    // roleName: '销售员',
    pageNum: 1,
    pageSize: 1000
  }).then(res => {
    selectData.salesUsers = res.data
  })
}

onMounted(() => {
  getSelectData()
  getDetail()
})
</script>
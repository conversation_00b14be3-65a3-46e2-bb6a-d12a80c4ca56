<template>
  <div class="fixed-view-container">
    <ViewHeader title="客户管理" add="新增客户" @add="addRef.open()" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
  </div>
</template>

<script>
export default {
  name: '/user/customer'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Api from '@/api/user/index.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.customer_list(query)
    return {list:records,total}
  },
  delApi: Api.customer_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '客户名称',
    key: 'name'
  },
]

const addRef = ref(null)
const handleData = {
  width: '140',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'name',
    label: '客户名称',
    width: ''
  },
  {
    prop: 'phone',
    label: '手机号',
    width: ''
  },
  {
    prop: 'linkName',
    label: '联系人',
    width: ''
  },
  {
    prop: 'linkPhone',
    label: '联系电话',
    width: ''
  },
  {
    prop: 'identityName',
    label: '用户类型',
    width: ''
  },
  {
    prop: 'saleName',
    label: '销售员',
    width: ''
  },
  {
    prop: 'strategyValName',
    label: '价格策略',
    width: ''
  },
]

</script>
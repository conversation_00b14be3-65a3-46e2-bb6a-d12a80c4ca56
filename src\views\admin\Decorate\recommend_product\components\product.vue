<template>
  <el-dialog
    width="900px"
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-button type="primary" size="small" @click="ProductSelectRef.open()">新增商品</el-button>
    <CommonTable
      class="mt10"
      heightType="fixed"
      height="400px"
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
  <ProductSelect ref="ProductSelectRef" @success="productSelect" />
</template>

<script setup>
import { ref } from "vue";
import CommonTable from '@/components/CommonTable.vue';
import Pagination from "@/components/Pagination.vue";
import ProductSelect from '@/views/admin/Product/components/productSelect.vue';
import Api from '@/api/decorate/index.js';
import { useList } from "@/hooks/useCommonTable.js";

const { tableData, pageParams, searchParams, getList, delItem, listReset } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdHotSale_infoProd(query)
    return {list:records,total}
  },
  delApi: Api.bdHotSale_delProd
})

const dialogVisible = ref(false)
const title = ref('商品列表')
const hotSaleId = ref(0)
const open = (id) => {
  if (id) {
    hotSaleId.value = id
    searchParams.value.infoId = id
    getList()
  }
  dialogVisible.value = true
}

const ProductSelectRef = ref(null)
const productSelect = (row) => {
  Api.bdHotSale_addProd({
    hotSaleId: hotSaleId.value,
    prodId: row.id
  }).then(res => {
    getList()
  })
}

const close = () => {
  listReset()
}

defineExpose({
  open
})

const handleData = {
  width: '100',
  handle: [
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'name',
    label: '商品名称',
    width: ''
  },
  {
    prop: 'brand',
    label: '品牌',
    width: ''
  },
  {
    prop: 'deliveryDate',
    label: '货期',
    width: ''
  },
  {
    prop: 'facePrice',
    label: '面价',
    width: ''
  },
  {
    prop: 'model',
    label: '型号',
    width: ''
  },
  {
    prop: 'orderNumber',
    label: '订货号',
    width: ''
  },
  {
    prop: 'unit',
    label: '计量单位',
    width: ''
  },
  {
    prop: 'minBuyNum',
    label: '最小起购量',
    width: ''
  },
]
</script>
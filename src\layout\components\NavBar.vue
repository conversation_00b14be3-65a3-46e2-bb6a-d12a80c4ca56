<template>
  <div class="flex-bc navbar-container">
    <div class="flex-bc flex-1">
      <div class="control-btn btn-hover flex-cc" @click="scrollStep(-300)">
        <el-icon><ArrowLeftBold /></el-icon>
      </div>
      <div class="navbar-list" ref="ScrollBar">
        <!-- <div class="navbar-item btn-hover flex-cc" @click="router.push('/dashboard')">
          <span class="tag" :class="{active: route.fullPath === '/dashboard/index'}"></span>
          <span class="ml10 mr10">概览</span>
        </div> -->
        <div class="navbar-item flex-cc" :class="{active: item.fullPath===route.fullPath}" v-for="item in tagList" :key="item.fullPath" @click="router.push(item.fullPath)">
          <span class="tag" :class="{active: item.fullPath===route.fullPath}"></span>
          <span class="tag-title">{{item.title}}</span>
          <el-icon :size="16" class="btn-close" v-if="!item.isLong" @click.stop="removeTag(item)"><Close /></el-icon>
        </div>
      </div>
      <div class="control-btn btn-hover flex-cc" @click="scrollStep(300)">
        <el-icon><ArrowRightBold /></el-icon>
      </div>
    </div>
    <div class="control-btn btn-hover ml5">
      <el-dropdown class="w100 h100" placement="bottom-end" trigger="click">
        <div class="w100 h100 flex-cc">
          <el-icon><ArrowDownBold /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="closeCur">关闭标签</el-dropdown-item>
            <el-dropdown-item @click="closeOthers">关闭其他标签</el-dropdown-item>
            <el-dropdown-item divided @click="closeAll">关闭所有标签</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
  import {ref, watch, computed, nextTick} from 'vue'
  import {useRoute,useRouter} from 'vue-router'
  import {useStore} from 'vuex'
  import { ArrowLeftBold, Close, ArrowRightBold, ArrowDownBold, Setting, Calendar, PieChart } from '@element-plus/icons-vue'

  const store = useStore()
  const tagList = computed(() => {
    return store.getters.tagList
  })

  const route = useRoute()

  const router = useRouter()
  const removeTag = (item) => {
    let noBackFlag = route.fullPath !== item.fullPath
    store.commit('REMOVE_TAG_ITEM', {
      fullPath: item.fullPath,
      noBackFlag
    })
  }

  const ScrollBar = ref(null)
  watch(route, () => {
    // 已有不添加
    if (tagList.value.some(item => item.fullPath === route.fullPath)) return;
    store.commit('ADD_TAG_ITEM', {
      fullPath: route.fullPath,
      title: route.meta.title
    })
    nextTick(() => {
      let dom = ScrollBar.value
      if (!dom) return false
      dom.scrollLeft = dom.scrollWidth
    })
  })
  const scrollStep = (number) => {
    let dom = ScrollBar.value
    let distance = dom.scrollWidth - dom.scrollLeft
    if(distance > 300 || distance < -300) {
      dom.scrollLeft += number
    } else if (distance < 0) {
      dom.scrollLeft = 0
    } else {
      dom.scrollLeft = dom.offsetWidth - dom.clientWidth
    }
  }

  const closeCur = () => {
    store.dispatch('closeCur')
  }
  const closeAll = () => {
    store.dispatch('closeAll')
  }
  const closeOthers = () => {
    store.dispatch('closeOthers')
  }
</script>

<style scoped lang="scss">
.navbar-container {
  background-color: $bg-color-light;
  padding: 10px 15px;
  width: 100%;
}
.control-btn {
  flex: 0 0 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #e8eaec;
  background-color: #fff;
}
.navbar-list {
  width: 0;
  flex: 1;
  display: flex;
  align-items: center;
  overflow-x: auto;
  scroll-behavior: smooth;
  &::-webkit-scrollbar{
    width: 0 !important;
    height: 0 !important;
  }
  .navbar-item {
    height: 30px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #e8eaec;
    padding: 0 8px;
    margin: 0 4px;
    flex: 0 0 auto;
    cursor: pointer;
    transition: all .3s;
    &:hover{
      color: $primary;
    }
    &.active{
      color: $primary;
    }
    .tag {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #eee;
      &.active{
        background-color: $primary;
      }
    }
    .tag-title{
      margin: 0 8px 0 8px;
    }
    .btn-close{
      padding: 2px;
      border-radius: 8px;
      transition: all .3s;
      &:hover{
        color: white;
        background-color: $primary;
      }
      
    }
  }
}


</style>
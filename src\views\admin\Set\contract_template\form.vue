<template>
  <div class="fixed-view-container">
    <ViewHeader title="合同模板" add="保存" @add="submit" />
    <div class="flex-1-roll">
      <el-form class="mt20" ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="模板类型">
          <el-radio-group v-model="form.identity" @change="identityChange">
            <el-radio-button :label="1">普通用户模板</el-radio-button>
            <el-radio-button :label="2">企业用户模板</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签订地址" prop="signAddress">
          <el-input v-model="form.signAddress"></el-input>
        </el-form-item>
        <el-form-item label="电子签章" prop="signetPath">
          <ImgUpload
            v-model="form.signetPath"
            :size="0.5"
            text="尺寸200x200,透明"
          />
        </el-form-item>
        <el-form-item label="条款内容" prop="text">
          <RichText v-model="form.text" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: '/set/contract_template'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import ImgUpload from '@/components/ImgUpload.vue';
import RichText from '@/components/RichText.vue';
import Api from '@/api/set/index.js';

const form = ref({
	"identity": 1,
	"signAddress": "",
	"signetPath": "",
	"text": ""
})
const rules = {
  signAddress: [{ required: true, message: '请填写签订地址', trigger: 'blur', }],
  signetPath: [{ required: true, message: '请上传电子签章', trigger: 'change', }],
  text: [{ required: true, message: '请填写条款内容', trigger: 'change', }],
}
const formRef = ref(null)
const submit = () => {
  formRef.value.validate(valid => {
    if (valid) {
      Api.bdCompact_save(form.value).then(res => {
        getDetail(form.value.identity)
      })
    }
  })
}

const getDetail = (identity=1) => {
  Api.bdCompact_info({
    identity
  }).then(res => {
    if (res.code === -10) return;
    form.value = res.data
  })
}
const identityChange = (val) => {
  getDetail(val)
}

onMounted(() => {
  getDetail()
})
</script>
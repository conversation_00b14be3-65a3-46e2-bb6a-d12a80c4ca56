<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title"></el-input>
      </el-form-item>
      <el-form-item label="类型">
        <el-radio-group v-model="form.type">
          <el-radio-button :label="1">备案信息</el-radio-button>
          <el-radio-button :label="2">友情链接</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="链接地址" prop="linkAddress">
        <el-input v-model="form.linkAddress"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import Api from '@/api/decorate/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
	"linkAddress": "",
	"title": "",
	"type": 1
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur', }],
  linkAddress: [{ required: true, message: '请输入链接地址', trigger: 'blur', }],
  content: [{ required: true, message: '请输入详情', trigger: 'change', }],
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.footerInformation_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'footerInformation_edit' : 'footerInformation_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
import Axios from '../axios'
import env from '../environment'
export default {

  // 轮播图
  jqSlideshowimage_add: params => Axios.post(`${env.API}admin/jqSlideshowimage/add`, params),
  jqSlideshowimage_del: params => Axios.post(`${env.API}admin/jqSlideshowimage/del`, params),
  jqSlideshowimage_edit: params => Axios.post(`${env.API}admin/jqSlideshowimage/edit`, params),
  jqSlideshowimage_info: params => Axios.post(`${env.API}admin/jqSlideshowimage/info`, params),
  jqSlideshowimage_list: params => Axios.post(`${env.API}admin/jqSlideshowimage/list`, params),

  // 首页宣传图
  bdPropaganda_add: params => Axios.post(`${env.API}admin/bdPropaganda/add`, params),
  bdPropaganda_del: params => Axios.post(`${env.API}admin/bdPropaganda/del`, params),
  bdPropaganda_edit: params => Axios.post(`${env.API}admin/bdPropaganda/edit`, params),
  bdPropaganda_info: params => Axios.post(`${env.API}admin/bdPropaganda/info`, params),
  bdPropaganda_list: params => Axios.post(`${env.API}admin/bdPropaganda/list`, params),

  // 推荐产品
  bdHotSale_add: params => Axios.post(`${env.API}admin/bdHotSale/add`, params),
  bdHotSale_del: params => Axios.post(`${env.API}admin/bdHotSale/del`, params),
  bdHotSale_edit: params => Axios.post(`${env.API}admin/bdHotSale/edit`, params),
  bdHotSale_list: params => Axios.post(`${env.API}admin/bdHotSale/list`, params),
  bdHotSale_info: params => Axios.post(`${env.API}admin/bdHotSale/info`, params),
  bdHotSale_addProd: params => Axios.post(`${env.API}admin/bdHotSale/addProd`, params),
  bdHotSale_delProd: params => Axios.post(`${env.API}admin/bdHotSale/delProd`, params),
  bdHotSale_infoProd: params => Axios.post(`${env.API}admin/bdHotSale/infoProd`, params),

  // 资讯
  bdRealtimeInfo_add: params => Axios.post(`${env.API}admin/bdRealtimeInfo/add`, params),
  bdRealtimeInfo_del: params => Axios.post(`${env.API}admin/bdRealtimeInfo/del`, params),
  bdRealtimeInfo_edit: params => Axios.post(`${env.API}admin/bdRealtimeInfo/edit`, params),
  bdRealtimeInfo_info: params => Axios.post(`${env.API}admin/bdRealtimeInfo/info`, params),
  bdRealtimeInfo_list: params => Axios.post(`${env.API}admin/bdRealtimeInfo/list`, params),

  // 底部指引
  footerMenu_add: params => Axios.post(`${env.API}admin/footerMenu/add`, params),
  footerMenu_del: params => Axios.post(`${env.API}admin/footerMenu/del`, params),
  footerMenu_edit: params => Axios.post(`${env.API}admin/footerMenu/edit`, params),
  footerMenu_info: params => Axios.post(`${env.API}admin/footerMenu/info`, params),
  footerMenu_list: params => Axios.post(`${env.API}admin/footerMenu/list`, params),

  // 底部信息
  footerInformation_add: params => Axios.post(`${env.API}admin/footerInformation/add`, params),
  footerInformation_del: params => Axios.post(`${env.API}admin/footerInformation/del`, params),
  footerInformation_edit: params => Axios.post(`${env.API}admin/footerInformation/edit`, params),
  footerInformation_info: params => Axios.post(`${env.API}admin/footerInformation/info`, params),
  footerInformation_list: params => Axios.post(`${env.API}admin/footerInformation/list`, params),
  
}
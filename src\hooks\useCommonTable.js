import { ref, reactive } from "vue";
import { Confirm } from '@/utils/global.js';

export function useList({
  getListApi = null, // 需要Promise类型
  delApi = null,
  defaultParams = {} // 默认参数
}) {
  const tableData = ref([])

  const pageParams = reactive({ // 分页参数
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const searchParams = ref({}) // 查询参数

  // 获取列表
  const getList = async () => {
    if (defaultParams) {
      Object.assign(searchParams.value, defaultParams)
    }
    if (!getListApi) return;
    const { list, total: items } = await getListApi({
      pageNum: pageParams.pageNum,
      pageSize: pageParams.pageSize,
      ...searchParams.value
    })
    pageParams.total = items
    tableData.value = list
  }

  // 删除
  const delItem = (id=null) => {
    if (!delApi) return;
    if (!id) return;
    Confirm(() => {
      delApi({id}).then(res => {
        getList()
      })
    });
  }

  // 重置
  const listReset = () => {
    tableData.value = []
    pageParams.pageNum = 1
    pageParams.pageSize = 10
    pageParams.total = 0
  }

  return { tableData, pageParams, searchParams, getList, delItem, listReset };
}

<template>
  <el-form :model="searchParams" :label-width="hideLabel ? '0' : '100px'">
    <el-row :gutter="10">
      <el-col :span="20">
        <el-row>
          <el-col :span="span" v-for="item in formData">
            <template v-if="item.type === 'input'">
              <el-form-item :label="hideLabel ? '' : item.label">
                <el-input
                  v-model="searchParams[item.key]"
                  :placeholder="item.placeholder || '请输入' + item.label"
                  clearable
                ></el-input>
              </el-form-item>
            </template>
            <template v-if="item.type === 'select'">
              <el-form-item :label="hideLabel ? '' : item.label">
                <el-select
                  :clearable="!(item.clearable === false)"
                  v-model="searchParams[item.key]"
                  :placeholder="'请选择' + item.label"
                  style="width:100%"
                >
                  <el-option
                    v-for="option in item.options"
                    :key="item.id || option.value"
                    :label="option.name"
                    :value="option.value || option.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
            <template v-if="item.type === 'switch'">
              <el-form-item :label="hideLabel ? '' : item.label">
                <el-switch
                  v-model="searchParams[item.key]"
                  :active-value="item.options.active"
                  :inactive-value="item.options.inactive"
                />
              </el-form-item>
            </template>
            <template v-if="item.type === 'date'">
              <el-form-item :label="hideLabel ? '' : item.label">
                <el-date-picker
                  v-model="searchParams[item.key]"
                  :type="'date'"
                  value-format="YYYY-MM-DD"
                  style="width:100%"
                  clearable
                  placeholder="请选择日期"
                ></el-date-picker>
              </el-form-item>
            </template>
            <template v-if="item.type === 'dateTime'">
              <el-form-item :label="hideLabel ? '' : item.label">
                <el-date-picker
                  style="width: 100%"
                  v-model="searchParams[item.key]"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  format="YYYY-MM-DD HH:mm:ss"
                  clearable
                  placeholder="请选择时间"
                ></el-date-picker>
              </el-form-item>
            </template>
            <template v-if="item.slot">
              <el-form-item :label="hideLabel ? '' : item.label">
                <slot :name="item.slot"></slot>
              </el-form-item>
            </template>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="4">
        <div class="flex-ec">
          <el-button type="primary" :icon="Search" @click="search">搜索</el-button>
          <el-button plain :icon="Refresh" @click="refresh">重置</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { Search, Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
    required: true
  },
  searchParams: {
    type: Object,
    default: () => ({}),
    required: true
  },
  span: {
    type: Number,
    default: 8
  },
  hideLabel: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['search'])

props.formData.forEach(item => {
  props.searchParams[item.key] = item.default || null;
});
const search = () => {
  emits('search')
}
const setValue = (values) => {
  Object.assign(props.searchParams, values)
}
const refresh = () => {
  Object.keys(props.searchParams).forEach(key => props.searchParams[key] = null)
  search()
}

defineExpose({
  setValue
})
</script>

<style scoped lang="scss">
.flex-ec{
  display: flex;
  justify-content: flex-end;
}
</style>
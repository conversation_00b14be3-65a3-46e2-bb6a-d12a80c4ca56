<template>
  <div class="fixed-view-container">
    <div class="flex-1-roll">
      <el-row :gutter="20" class="mt10">
        <el-col :span="6">
          <div class="count-item pink btn-hover">
            <div class="count">{{ allData.viewVolume }}</div>
            <div class="title">今日浏览量</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="count-item blue btn-hover">
            <div class="count">{{ allData.dealNumber }}</div>
            <div class="title">今日订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="count-item green btn-hover">
            <div class="count">{{ allData.dealAmount }}</div>
            <div class="title">今日订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="count-item red btn-hover">
            <div class="count">{{ allData.addNewCustomer }}</div>
            <div class="title">今日新增会员数</div>
          </div>
        </el-col>
      </el-row>
      <div class="chart-panel mt20">
        <div class="title flex-bc">
          <span>成交订单数据（近30日）</span>
        </div>
        <div class="chart-body">
          <div style="width: 100%; height: 100%" id="MyLineChart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 需要在index.html中引入echarts相关cdn资源，如资源失效请自行更换链接
 */
import { onMounted, ref } from "vue";
import Api from '@/api/common/index.js';

let option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  color: ['#0daca7', '#ff9843', '#ff3187', '#6286fe', '#e63435'],
  legend: {
    data: ['销售金额', '订单数量']
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false
    },
    axisPointer: {
      type: 'shadow'
    },
    data: []
  },
  yAxis: [
    {
      type: 'value',
      name: '销售金额',
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    {
      show: false,
      type: 'value',
      name: '订单数量'
    },
  ],
  series: [
    {
      name: '销售金额',
      data: [],
      type: 'bar'
    },
    {
      name: '订单数量',
      data: [],
      type: 'line',
      yAxisIndex: 1
    },
  ],
  grid: {
    top: '15%',
    left: '3%',
    right: '2%',
    bottom: '3%',
    containLabel: true
  },
}

let MyLineChart = null
const allData = ref({})
const getData = () => {
  Api.index_statisticalData().then(res => {
    allData.value = res.data
    let { dealDatas } = res.data
    option.xAxis.data = dealDatas.map(item => item.date)
    option.series[0].data = dealDatas.map(item => item.money)
    option.series[1].data = dealDatas.map(item => item.number)
    MyLineChart.setOption(option)
  })
}
onMounted(() => {
  let lineChartDom = document.getElementById('MyLineChart')
  lineChartDom.removeAttribute('_echarts_instance_') // 解决页面切换后不显示问题
  MyLineChart = echarts.init(lineChartDom)

  getData()
})
</script>

<style scoped lang="scss">
.count-item {
  height: 120px;
  padding: 20px;
  color: #fff;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &.pink {
    background-image: linear-gradient(to bottom right, #d176f4, #6286fe);
    box-shadow: 8px 8px 16px rgb(98 134 254 / 50%);
  }

  &.blue {
    background-image: linear-gradient(to bottom right, #35c3fc, #008cff);
    box-shadow: 8px 8px 16px rgba(4, 174, 253, 0.5);
  }
  
  &.green {
    background-image: linear-gradient(to bottom right, #52e4be, #0daca7);
    box-shadow: 8px 8px 16px rgb(13 172 167 / 50%);
  }

  &.yellow {
    background-image: linear-gradient(to bottom right, #ffc65b, #ff9843);
    box-shadow: 8px 8px 16px rgb(255 152 67 / 50%);
  }

  &.red {
    background-image: linear-gradient(to bottom right, #fe9896, #e63435);
    box-shadow: 8px 8px 16px rgb(230 52 53 / 50%);
  }

  .count {
    font-size: 32px;
    font-weight: bold;
    line-height: 1.5;
  }

  .title {
    font-size: 16px;
    line-height: 1.5;
  }
}
.chart-panel {
  background-color: #fff;
  border-radius: 8px;
  padding: 10px;

  .title {
    padding: 5px 0 10px 0;
    border-bottom: 1px solid #eee;
    line-height: 32px;

    .title-text {
      color: $primary;
      font-weight: bold;
    }
  }

  .chart-body {
    height: 400px;
  }
}
</style>
<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="属性名称" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-divider content-position="center">规格值</el-divider>
      <el-button type="primary" size="small" @click="addValue">新增规格值</el-button>
      <el-row :gutter="10">
        <el-col :span="8" v-for="(item,index) in form.prodPropValues" :key="index">
          <div class="mt10 flex-c">
            <el-input v-model="item.value"></el-input>
            <el-button type="danger" plain :icon="Delete" circle @click="delValue(index)" />
          </div>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { Delete } from '@element-plus/icons-vue'
import { DeepClone } from '@/utils/global.js'
import Api from '@/api/product/index.js';

const emits = defineEmits(['success'])

const defaultValue = {
  id: null,
  value: ''
}
const defaultForm = {
  id: null,
  name: "",
  prodPropValues: [
    DeepClone(defaultValue)
  ]
}
const form = ref(DeepClone(defaultForm))
const addValue = () => {
  if (Array.isArray(form.value.prodPropValues)) {
    form.value.prodPropValues.push(DeepClone(defaultValue))
  } else {
    form.value.prodPropValues = [
      DeepClone(defaultValue)
    ]
  }
}
const delValue = (index) => {
  form.value.prodPropValues.splice(index, 1)
}

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  name: [{ required: true, message: '请填写属性名称', trigger: 'blur', }],
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.bdProdPop_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'bdProdPop_edit' : 'bdProdPop_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
<template>
  <div class="fixed-view-container">
    <ViewHeader title="推荐产品" add="新增产品组" @add="addRef.open()" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
    <Product ref="ProductRef" />
  </div>
</template>

<script>
export default {
  name: '/decorate/recommend_product'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Product from "./components/product.vue";
import Api from '@/api/decorate/index.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdHotSale_list(query)
    return {list:records,total}
  },
  delApi: Api.bdHotSale_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '产品组名称',
    key: 'titleText'
  },
]

const addRef = ref(null)
const ProductRef = ref(null)
const handleData = {
  width: '220',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '编辑商品',
      fun: (row) => {
        ProductRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'titleText',
    label: '产品组名称',
    width: ''
  },
  {
    prop: 'titleColor',
    label: '颜色',
    width: ''
  },
]

</script>
<template>
  <div class="mt10 flex-bc">
    <div></div>
    <el-pagination :small="small" background :layout="layout" v-model:current-page="pageParams.pageNum"
      :page-sizes="pageSizes" v-model:page-size="pageParams.pageSize" :total="pageParams.total" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />
    <!-- <el-pagination background layout="total, sizes, prev, pager, next" :total="50" /> -->
  </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  pageSizes: {
    type: Array,
    default: () => {
      return [10, 20, 50, 100]
    }
  },
  pageParams: {
    type: Object,
    default: () => ({
      pageNum: 1,
      pageSize: 10,
      total: 1
    }),
    required: true
  },
  small: {
    type: Boolean,
    default: false
  }
})

const layout = computed(() => {
  if (props.small) return 'prev, pager, next';
  return 'total, sizes, prev, pager, next'
})

const emits = defineEmits(['handle'])

const handleSizeChange = (val) => {
  pageParams.pageNum = 1
  emits('handle')
}
const handleCurrentChange = (val) => {
  emits('handle')
}
</script>
<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title"></el-input>
      </el-form-item>
      <el-form-item label="摘要" prop="outline">
        <el-input v-model="form.outline"></el-input>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch v-model="form.state" />
      </el-form-item>
      <el-form-item label="图片" prop="imgLink">
        <ImgUpload
          v-model="form.imgLink"
          :size="2"
          text="建议尺寸"
        />
      </el-form-item>
      <el-form-item label="内容" prop="context">
        <RichText v-model="form.context"></RichText>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import ImgUpload from '@/components/ImgUpload.vue';
import RichText from '@/components/RichText.vue';
import Api from '@/api/decorate/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
	"context": "",
	"imgLink": "",
	"outline": "",
	"state": false,
	"title": ""
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur', }],
  outline: [{ required: true, message: '请输入摘要', trigger: 'blur', }],
  imgLink: [{ required: true, message: '请上传图片', trigger: 'change', }],
  context: [{ required: true, message: '请输入详情', trigger: 'change', }],
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.bdRealtimeInfo_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'bdRealtimeInfo_edit' : 'bdRealtimeInfo_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
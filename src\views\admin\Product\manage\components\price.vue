<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="普通期货折扣">
            <el-input v-model="form.futuresDiscount"></el-input>
          </el-form-item>
          <el-form-item label="一级期货折扣">
            <el-input v-model="form.oneFuturesDiscount"></el-input>
          </el-form-item>
          <el-form-item label="二级期货折扣">
            <el-input v-model="form.twoFuturesDiscount"></el-input>
          </el-form-item>
          <el-form-item label="三级期货折扣">
            <el-input v-model="form.threeFuturesDiscount"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="普通现货折扣">
            <el-input v-model="form.spotDiscount"></el-input>
          </el-form-item>
          <el-form-item label="一级现货折扣">
            <el-input v-model="form.oneSpotDiscount"></el-input>
          </el-form-item>
          <el-form-item label="二级现货折扣">
            <el-input v-model="form.twoSpotDiscount"></el-input>
          </el-form-item>
          <el-form-item label="三级现货折扣">
            <el-input v-model="form.threeSpotDiscount"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import Api from '@/api/product/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
  prodId: null,
	"futuresDiscount": 0,
	"oneFuturesDiscount": 0,
	"oneSpotDiscount": 0,
	"spotDiscount": 0,
	"threeFuturesDiscount": 0,
	"threeSpotDiscount": 0,
	"twoFuturesDiscount": 0,
	"twoSpotDiscount": 0
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.bdProdStrategyPrice_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      Api.bdProdStrategyPrice_edit(form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
<template>
  <div class="full-bg flex-cc">
    <div class="flex-col-cc">
      <div class="flex-cc">
        <div class="flex-cc">
          <img class="logo" src="@/assets/images/dianzimiandan-logo.png" />
          <div class="prd-name">在线商城后台管理系统</div>
        </div>
      </div>
      <div class="login-panel">
        <div class="title">用户登录</div>
        <el-form :model="form" :rules="loginRules" ref="loginForm" size="large">
          <el-form-item prop="account">
            <el-input class="mt30" v-model="form.account" placeholder="请输入账号">
              <template #prepend>
                <el-button :icon="User"></el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input type="password" v-model="form.password" placeholder="请输入密码">
              <template #prepend>
                <el-button :icon="Lock"></el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="flex-bc">
          <el-checkbox class="ml10" v-model="checked">记住密码</el-checkbox>
          <!-- <div class="link fz14" @click="router.push('/retrieve_pwd')">忘记密码？</div> -->
        </div>
        <div class="login-btn btn-hover mt30" @click="login">登 录</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import md5 from "js-md5";
import { ref, reactive, onBeforeMount } from "vue";
import { useStore } from 'vuex'
import { useRouter } from "vue-router";
import { ElNotification } from "element-plus";
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const store = useStore()

let form = reactive({
  account: "",
  password: "",
});
const loginForm = ref(null);
const loginRules = ref({
  account: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
});
const checked = ref(false)
const login = () => {
  loginForm.value.validate(valid => {
    if (valid) {
      store.dispatch('login', {
        account: form.account,
        password: md5(form.password),
      }).then(res => {
        store.dispatch('getConstant')
        ElNotification({
          title: "成功",
          message: "登录成功",
          type: "success",
        });
        if (checked.value) {
          localStorage.setItem('accountInfo',
            JSON.stringify({
              account: form.account,
              password: window.btoa(form.password)
            })
          )
        } else {
          localStorage.removeItem('accountInfo')
        }
        router.push('/dashboard')
      })
    }
  })
}

onBeforeMount(() => {
  let accountInfo = localStorage.getItem('accountInfo')
  if (accountInfo) {
    checked.value = true
    let accountInfoObj = JSON.parse(accountInfo)
    form.account = accountInfoObj.account
    form.password = window.atob(accountInfoObj.password)
  }
  store.commit('RESET_TAG_ITEM')
  store.commit('SET_USER_INFO', {})
})

</script>

<style scoped lang="scss">
.full-bg {
  height: 100vh;
  background: linear-gradient(to bottom, #132831, #203a43, #2c5364);
}
.logo {
  height: 40px;
}
.prd-name {
  color: #fff;
  font-size: 24px;
  margin-left: 5px;
}
.port-text {
  font-size: 16px;
  color: #fff;
  padding-left: 10px;
  border-left: 1px solid rgba(255, 255, 255, 0.5);
  margin-left: 10px;
}
.login-panel {
  margin-top: 50px;
  width: 400px;
  background-color: #fff;
  border-radius: 4px;
  height: 400px;
  padding: 20px;
  .title {
    margin-top: 20px;
    font-size: 24px;
  }
}
.login-btn {
  background: linear-gradient(to right, #49a7ff, #0051ff);
  border-radius: 4px;
  text-align: center;
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: #fff;
  font-weight: bold;
}
</style>
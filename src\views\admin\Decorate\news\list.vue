<template>
  <div class="fixed-view-container">
    <ViewHeader title="资讯管理" add="新增资讯" @add="addRef.open()" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
      <template #imgLink="{row}">
        <img :src="row.imgLink" style="max-height:120px;" />
      </template>
      <template #state="{row}">
        <el-tag type="success">{{ row.state ? '启用' : '禁用' }}</el-tag>
      </template>
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
  </div>
</template>

<script>
export default {
  name: '/decorate/news'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Api from '@/api/decorate/index.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdRealtimeInfo_list(query)
    return {list:records,total}
  },
  delApi: Api.bdRealtimeInfo_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '标题',
    key: 'title'
  },
]

const addRef = ref(null)
const handleData = {
  width: '140',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    slot: 'imgLink',
    label: '图片',
    width: ''
  },
  {
    prop: 'title',
    label: '标题',
    width: ''
  },
  {
    prop: 'outline',
    label: '摘要',
    width: ''
  },
  {
    slot: 'state',
    label: '状态',
    width: ''
  },
]

</script>
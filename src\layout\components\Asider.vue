<template>
  <div class="menu-container">
    <div class="one-menu-container">
      <div class="flex-cc logo-container">
        <img src="@/assets/images/dianzimiandan-logo.png" />
      </div>
      <div class="menu scrollbar-none">
        <div
          class="menu-item flex-cc"
          :class="{ active: activePath.startsWith(item.path) }"
          v-for="(item,index) in routerList"
          :key="index"
          @click="router.push(item.path)"
        >
          <el-icon :size="20">
            <component :is="item.meta.icon" />
	        </el-icon>
          <span class="label">{{ item.meta.title }}</span>

          <div class="second-menu-container" v-if="!item.meta.onlyOneLevel">
            <div
              class="second-menu-item"
              :class="{ active: fullPath == obj.path }"
              v-for="(obj,index) in item.children.filter(v => !v.meta.hidden)"
              :key="index"
              @click.stop="secondJump(obj)"
            >{{ obj.meta.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { routes } from '@/router';
import { useRoute, useRouter } from "vue-router";

const routerList = routes.filter(v => !v.meta.hidden) // 过滤不显示的

const route = useRoute()
const router = useRouter()
const activePath = computed(() => {
  return route.path
})
const fullPath = computed(() => {
  return route.fullPath
})

const secondJump = (routeItem) => {
  let query = routeItem.meta.query
  if (query) {
    router.push({
      path: routeItem.path,
      query
    })
  } else {
    router.push(
      routeItem.path
    )
  }
}
</script>

<style scoped lang="scss">
.menu-container {
  width: 100%;
  height: 100%;
}
.logo-container {
  flex: 0 0 auto;
  height: 60px;
  img {
    height: 32px;
  }
}
.scrollbar-none{
  &::-webkit-scrollbar{
    display: none;
  }
}
.one-menu-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .menu {
    flex: 1;
    overflow: auto;
    .menu-item {
      height: 60px;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      transition: all .3s;
      .second-menu-container{
        display: none;
      }
      &:hover{
        color: $primary;
        background-color: #292b3a;
        .second-menu-container{
          display: block;
        }
      }
      &.active {
        background-color: $primary;
        color: #fff;
        &:hover{
          color: #fff;
        }
      }
      i {
        font-size: 24px;
        margin-right: 5px;
      }
      .label {
        font-size: 16px;
      }
    }
  }
}
.second-menu-container {
  position: absolute;
  z-index: 999;
  left: 100px;
  top: 60px;
  border-right: 1px solid #eee;
  width: 160px;
  height: calc(100vh - 60px);
  background-color: #1f202c;
  overflow: auto;
  .second-menu-item {
    margin: 10px 10px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    color: rgb(180, 180, 180);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    &:hover{
      color: $primary;
      background-color: #292b3a;
    }
    &.active {
      color: #fff;
      background-color: $primary;
    }
  }
}
</style>
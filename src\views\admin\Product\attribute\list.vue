<template>
  <div class="fixed-view-container">
    <ViewHeader title="商品规格属性" add="新增属性" @add="addRef.open()" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
  </div>
</template>

<script>
export default {
  name: '/product/attribute'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Api from '@/api/product/index.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdProdPop_list(query)
    return {list:records,total}
  },
  delApi: Api.bdProdPop_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '属性名称',
    key: 'name'
  },
]

const addRef = ref(null)
const handleData = {
  width: '140',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'name',
    label: '属性名称',
    width: ''
  },
  {
    prop: 'updateTime',
    label: '上次修改时间',
    width: ''
  },
]

</script>
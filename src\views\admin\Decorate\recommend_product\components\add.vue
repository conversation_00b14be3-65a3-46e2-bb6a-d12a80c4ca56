<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="产品组标题" prop="titleText">
        <el-input v-model="form.titleText"></el-input>
      </el-form-item>
      <el-form-item label="颜色">
        <el-color-picker v-model="form.titleColor" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import Api from '@/api/decorate/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
  titleColor: "#409EFF",
  titleText: "",
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  titleText: [{ required: true, message: '请填写标题', trigger: 'blur', }],
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.bdHotSale_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'bdHotSale_edit' : 'bdHotSale_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
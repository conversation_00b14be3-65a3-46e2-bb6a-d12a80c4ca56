<template>
  <div class="img-box" v-loading="loading">
    <el-upload
      v-if="!modelValue"
      class="btn-upload"
      action="uploadUrl"
      :http-request="logoImgChange"
      :show-file-list="false"
    >
      <div class="text-container">
        <el-icon :size="20"><Plus /></el-icon>
        <template v-if="cssWidth === '120px'">
          <p class="mt10">图片大小{{size}}M内</p>
          <p>
            格式
            <span v-for="(item,index) in types" :key="index">{{item}}&nbsp;</span>
          </p>
          <p>{{text}}</p>
        </template>
      </div>
    </el-upload>
    <div v-else class="img-container">
      <img :src="modelValue" />
    </div>
    <ul class="btn-list" v-if="modelValue">
      <li class="btn-item" @click="del">
        <el-icon :size="12"><Close /></el-icon>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref } from "vue";
import oss from '@/api/oss/oss.js'
import { ElNotification } from 'element-plus';
import { Close, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
    default: ''
  },
  size: {
    type: Number,
    required: false,
    default: 1
  },
  types: {
    type: Array,
    required: false,
    default: () => ['jpg','jpeg','png']
  },
  text: {
    type: String,
    required: false,
    default: ''
  },
  cssWidth: {
    type: String,
    required: false,
    default: '120px'
  }
})
const emits = defineEmits(['update:modelValue'])

const loading = ref(false)

const logoImgChange = content => {
  const { file } = content
  const fix = file.name.substr(file.name.lastIndexOf('.') + 1)
  if (!props.types.includes(fix)) return ElNotification.error('图片格式不正确')
  const limit = file.size / 1024 / 1024 < props.size
  if (!limit) return ElNotification.error(`图片过大，需在${props.size}M内`)

  loading.value = true
  oss.uploadFile(file).then(res => {
    emits('update:modelValue', res.url)
  }).finally(() => {
    loading.value = false
  })
}

const del = () => {
  emits('update:modelValue', '')
}
</script>

<style scoped lang="scss">
.img-box {
  position: relative;
  width: v-bind('props.cssWidth');
  overflow: hidden;
  .img-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: v-bind('props.cssWidth');
    height: v-bind('props.cssWidth');
    box-sizing: border-box;
    border: 1px solid #e6e6e6;
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  .btn-upload {
    border: 1px dashed #ccc;
    border-radius: 6px;
    transition: all 0.3s;
    cursor: pointer;
    &:hover {
      border: 1px dashed #1989fa;
      color: #1989fa;
    }
    .text-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: v-bind('props.cssWidth');
      height: v-bind('props.cssWidth');
      i {
        font-size: 30px;
      }
      p {
        margin: 0;
        font-size: 12px;
        line-height: 24px;
        color: #999;
      }
    }
  }
  .btn-list {
    margin: 0;
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0.8;
    .btn-item {
      color: white;
      padding: 8px;
      font-size: 12px;
      line-height: 12px;
      border-right: 1px solid white;
      cursor: pointer;
      background-color: #a0a0a0;
    }
  }
}
</style>

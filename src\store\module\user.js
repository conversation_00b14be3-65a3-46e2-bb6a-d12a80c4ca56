import API from "@/api/login/index.js";

const state = {
  userInfo: {},
};

const getters = {
  userInfo: (state) => state.userInfo,
};

const mutations = {
  SET_USER_INFO(state, obj) {
    state.userInfo = obj;
  },
};

const actions = {
  login({ commit }, params) {
    return new Promise((resolve, reject) => {
      let userInfo = null;
      API.login(params)
        .then((res) => {
          userInfo = res.data;
          commit("SET_USER_INFO", res.data);
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};

export default {
  state,
  getters,
  mutations,
  actions,
};

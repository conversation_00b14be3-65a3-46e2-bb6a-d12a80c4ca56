<template>
  <el-dialog :title="title" v-model="dialogVisible" @close="close" :close-on-click-modal="false"
    width="900px"
    :close-on-press-escape="false">
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="first">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="商品名称" prop="name">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="form.brand"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号" prop="model">
                <el-input v-model="form.model"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订货号" prop="orderNumber">
                <el-input v-model="form.orderNumber"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品类目" prop="categoryId">
                <div style="display: flex; gap: 8px;">
                  <el-cascader
                    v-model="form.categoryArray"
                    :props="{
                      value: 'id',
                      label: 'name',
                      checkStrictly: true
                    }"
                    :options="selectData.categoryTree"
                    @change="cascaderChange"
                    style="flex: 1;"
                  />
                  <el-button @click="clearCategory" size="default">清空选择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="货期" prop="deliveryDate">
                <el-input v-model="form.deliveryDate"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="面价" prop="facePrice">
                <el-input v-model="form.facePrice"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否包邮">
                <el-switch v-model="form.freeShipping" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计量单位" prop="unit">
                <el-input v-model="form.unit"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最小起购量">
                <el-input v-model="form.minBuyNum"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="简要描述">
                <el-input v-model="form.describe" :rows="5" type="textarea"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品主图">
                <ImgUpload v-model="form.pic" :size="1" text="建议尺寸800x800" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="规格属性" name="second">
        <el-button type="primary" @click="addRow">新增一行</el-button>
        <CommonTable
          class="mt10"
          :tableHeader="tableHeader"
          :tableData="form.properties"
          :handleData="handleData"
        >
          <template #name="{ row, $index }">
            <el-select v-model="row.id" style="width: 100%" @change="val => prodPopChange(val,row)">
              <el-option
                v-for="item in selectData.prodPopList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </template>
          <template #value="{ row }">
            <el-select v-model="row.value" filterable style="width: 100%">
              <el-option
                v-for="item in row.valueList"
                :key="item.id"
                :label="item.value"
                :value="item.id"
              ></el-option>
            </el-select>
          </template>
        </CommonTable>
      </el-tab-pane>
      <el-tab-pane label="详情" name="third">
        <RichText v-model="form.content" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, toRaw } from "vue";
import ImgUpload from '@/components/ImgUpload.vue';
import CommonTable from '@/components/CommonTable.vue';
import RichText from '@/components/RichText.vue';
import Api from '@/api/product/index.js';
import { DeepClone } from '@/utils/global.js'

const emits = defineEmits(['success'])

const activeName = ref('first')
const defaultProperties = {
  "id": null,
  "name": "",
  "value": null,
  valueList: []
}
const defaultForm = {
  "brand": "",
  "categoryId": null,
  "categoryArray": [],
  "content": "",
  "deliveryDate": "",
  "describe": "",
  "facePrice": 0,
  "freeShipping": false,
  "id": null,
  "minBuyNum": 0,
  "model": "",
  "name": "",
  "orderNumber": "",
  "pic": "",
  "properties": [
    DeepClone(defaultProperties)
  ],
  "status": 0,
  "unit": "",
  "volume": 0,
  "weight": 0
}
const form = ref(DeepClone(defaultForm))
const addRow = () => {
  form.value.properties.push(DeepClone(defaultProperties))
}
const cascaderChange = value => {
  form.value.categoryId = toRaw(value).at(-1)
}

const clearCategory = () => {
  form.value.categoryArray = []
  form.value.categoryId = null
}

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  brand: [{ required: true, message: '请填写品牌', trigger: 'blur', }],
  categoryId: [{ required: true, message: '请选择商品类目', trigger: 'change', }],
  deliveryDate: [{ required: true, message: '请填写货期', trigger: 'blur', }],
  facePrice: [{ required: true, message: '请填写面价', trigger: 'blur', }],
  model: [{ required: true, message: '请填写型号', trigger: 'blur', }],
  name: [{ required: true, message: '请填写商品名称', trigger: 'blur', }],
  orderNumber: [{ required: true, message: '请填写订货号', trigger: 'blur', }],
  unit: [{ required: true, message: '请填写计量单位', trigger: 'blur', }],
}
const getParentIds = (list = [], id = 0) => {
  for (let item of list) {
    if (item.id === id && id !== 0) {
      return [item.id]
    }
    if (item.children && item.children.length > 0) {
      let node = getParentIds(item.children, id)
      if (node !== undefined) {
        return [item.id].concat(node)
      }
    }
  }
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.bdProd_info({
      infoId: id
    }).then(res => {
      form.value = res.data
      if (form.value.categoryId && form.value.categoryId > 0) { // 联级组件回显
        form.value.categoryArray = getParentIds(selectData.categoryTree, form.value.categoryId)
      }
      let {properties} = form.value
      if (properties && properties.length > 0) { // 规格属性回显
        Api.bdProdPop_batchInfo({
          ids: properties.map(item => item.id)
        }).then(res => {
          properties.forEach(item => {
            let valueList = res.data.find(j => j.id === item.id)
            item.valueList = valueList.prodPropValues
          })
        })
      }
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'bdProd_edit' : 'bdProd_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

const selectData = reactive({
  categoryTree: [],
  prodPopList: [],
})
const getSelectData = () => {
  Api.bdCategory_treeList().then(res => {
    selectData.categoryTree = res.data
  })
  Api.bdProdPop_list({
    pageNum: 1,
    pageSize: 1000,
  }).then(res => {
    selectData.prodPopList = res.records
  })
}
onMounted(() => {
  getSelectData()
})

const tableHeader = [
  {
    slot: 'name',
    label: '选择规格',
    width: ''
  },
  {
    slot: 'value',
    label: '规格值',
    width: ''
  }
]

const handleData = {
  width: '100',
  handle: [
    {
      label: '删除',
      fun: (row, index) => {
        form.value.properties.splice(index, 1)
      }
    },
  ]
}

const prodPopChange = (val, row) => {
  Api.bdProdPop_info({
    infoId: val
  }).then(res => {
    row.valueList = res.data?.prodPropValues ?? []
    row.value = null
  })
}

defineExpose({
  open
})
</script>
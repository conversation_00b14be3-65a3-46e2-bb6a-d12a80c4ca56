<template>
  <div class="router-view-container" :class="{ bg: !(meta.type==='bg_none') }">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :max="10" :include="tagList">
          <component v-if="route.meta.keepAlive" :is="Component" :key="route.fullPath" />
        </keep-alive>
      </transition>
      <component v-if="!route.meta.keepAlive" :is="Component" :key="route.fullPath" />
    </router-view>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";
import { useStore } from 'vuex'

const store = useStore()
const tagList = computed(() => {
  return store.getters.tagList.map(item => item.fullPath)
})

const route = useRoute()
const meta = computed(() => {
  return route.meta
})
</script>

<style scoped lang="scss">
.router-view-container {
  width: 100%;
  height: 100%;
  &.bg {
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>
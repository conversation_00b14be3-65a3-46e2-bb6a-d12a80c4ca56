<template>
  <div class="view-title border flex-bc">
    <div class="flex-cc">
      <el-button round :icon="Back" class="mr10" v-if="props.back" @click="back">返回</el-button>
      <div class="fz16">{{title}}</div>
    </div>
    <div>
      <el-button type="primary" plain :icon="Plus" v-if="add" @click="emits('add')">{{add}}</el-button>
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import {useStore} from 'vuex'
import { Plus, Back } from '@element-plus/icons-vue'

const store = useStore()
const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  back: {
    type: [Boolean, String],
    default: false
  },
  add: {
    type: String,
    default: ''
  },
})

const emits = defineEmits(['add'])

const back = () => {
  const type = typeof props.back
  if (type === 'boolean') { // 没有指定页面，默认回到上一个标签页
    store.dispatch('closeCur')
  }
  if (type === 'string') { // 指定了返回的路径
    store.dispatch('closeCur', props.back)
  }
}
</script>
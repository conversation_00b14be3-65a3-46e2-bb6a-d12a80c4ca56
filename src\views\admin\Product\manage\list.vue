<template>
  <div class="fixed-view-container">
    <ViewHeader title="商品管理" add="新增商品" @add="addRef.open()">
      <el-button type="success" plain @click="batchAttribute">批量修改规格属性</el-button>
    </ViewHeader>
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
      :options="{ isSelection: true }"
      v-model:selectData="selectData"
    >
      <template #status="{row}">
        <el-switch v-model="row.status" :active-value="1" :inactive-value="0" @change="val => statusChange(val,row)" />
      </template>
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
    <Price ref="priceRef" @success="getList" />
    <NumberList ref="numberListRef" />
    <BatchAttribute ref="batchAttributeRef" />
  </div>
</template>

<script>
export default {
  name: '/product/manage'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Price from "./components/price.vue";
import NumberList from "./components/numberList.vue";
import BatchAttribute from "./components/batchAttribute.vue";
import Api from '@/api/product/index.js';
import { ElNotification } from "element-plus";

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdProd_list(query)
    return {list:records,total}
  },
  delApi: Api.bdProd_del
})
onMounted(() => {
  getList()
})

const selectData = ref([])
const batchAttributeRef = ref(null)
const batchAttribute = () => {
  ElNotification.warning
  if (selectData.value.length === 0) return ElNotification.warning('请选择要修改的产品')
  batchAttributeRef.value.open(selectData.value.map(item => item.id))
}

const formData = [
  {
    type: 'input',
    label: '商品名称',
    key: 'name'
  },
  {
    type: 'input',
    label: '订货号',
    key: 'orderNumber'
  },
  {
    type: 'input',
    label: '型号',
    key: 'model'
  },
]

const addRef = ref(null)
const priceRef = ref(null)
const numberListRef = ref(null)
const handleData = {
  width: '280',
  handle: [
    {
      label: '价格策略',
      fun: (row) => {
        priceRef.value.open(row.id)
      }
    },
    {
      label: '数量价格',
      fun: (row) => {
        numberListRef.value.open(row.id)
      }
    },
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}
const statusChange = (val,{id}) => {
  if (!id) return;
  Api.bdProd_updateState({
    id,
    status: val
  }).then(res => {
    getList()
  })
}

const tableHeader = [
  {
    prop: 'name',
    label: '商品名称',
    width: ''
  },
  {
    prop: 'brand',
    label: '品牌',
    width: ''
  },
  {
    prop: 'deliveryDate',
    label: '货期',
    width: ''
  },
  {
    prop: 'facePrice',
    label: '面价',
    width: ''
  },
  {
    prop: 'model',
    label: '型号',
    width: ''
  },
  {
    prop: 'orderNumber',
    label: '订货号',
    width: ''
  },
  {
    prop: 'unit',
    label: '计量单位',
    width: ''
  },
  {
    prop: 'minBuyNum',
    label: '最小起购量',
    width: ''
  },
  {
    slot: 'status',
    fixed: 'right',
    label: '是否上架',
    width: '100px'
  },
]

</script>
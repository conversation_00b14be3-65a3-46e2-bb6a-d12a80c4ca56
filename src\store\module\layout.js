import router from '@/router/index.js'

const state = {
  tagList: [
    {
      title: '概览',
      fullPath: '/dashboard/index',
      isLong: true
    }
  ]
}

const getters = {
  tagList: (state) => state.tagList,
  keepAliveNames: (state) => state.tagList.map(item => item.fullPath),
}

const mutations = {
  ADD_TAG_ITEM(state, obj) {
    state.tagList.push(obj)
  },
  REMOVE_TAG_ITEM(state, obj) {
    const index = state.tagList.findIndex(item => item.fullPath === obj.fullPath)
    const redirectUrl = state.tagList[index - 1]?.fullPath
    state.tagList.splice(index, 1)
    if(obj.noBackFlag) return
    if (obj.to) {
      router.replace(obj.to)
    } else {
      router.replace(redirectUrl)
    }
  },
  RESET_TAG_ITEM(state) {
    state.tagList = [
      {
        title: '概览',
        fullPath: '/dashboard/index',
        isLong: true
      }
    ]
  }
}

const actions = {
  closeCur({commit}, to='') {
    commit('REMOVE_TAG_ITEM', {
      fullPath: router.currentRoute.value.fullPath,
      to
    })
  },
  closeAll({commit}) {
    commit('RESET_TAG_ITEM')
    router.push('/dashboard/index')
  },
  closeOthers({commit}) {
    commit('RESET_TAG_ITEM')
    if (router.currentRoute.value.fullPath === '/dashboard/index') return
    commit('ADD_TAG_ITEM', {
      fullPath: router.currentRoute.value.fullPath,
      title: router.currentRoute.value.meta.title
    })
  }
}

export default {
  state,
  getters,
  mutations,
  actions
}
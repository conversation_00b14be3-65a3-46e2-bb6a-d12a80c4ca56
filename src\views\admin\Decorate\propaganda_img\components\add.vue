<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="宣传图类型">
        <el-radio-group v-model="form.type">
          <el-radio-button :label="1">热卖头图</el-radio-button>
          <el-radio-button :label="2">热卖子项目图</el-radio-button>
          <el-radio-button :label="3">宽幅宣传图</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="定位" v-if="form.type === 2">
        <el-radio-group v-model="form.location">
          <el-radio-button :label="1">1</el-radio-button>
          <el-radio-button :label="2">2</el-radio-button>
          <el-radio-button :label="3">3</el-radio-button>
          <el-radio-button :label="4">4</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="图片" prop="imagePath">
        <ImgUpload
          v-model="form.imagePath"
          :size="2"
          :text="textArr[form.type]"
        />
      </el-form-item>
      <el-form-item label="跳转地址">
        <el-input v-model="form.link"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import ImgUpload from '@/components/ImgUpload.vue';
import Api from '@/api/decorate/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
	"imagePath": "",
	"link": "",
  location: 1,
  type: 1
}
const form = ref(defaultForm)
const textArr = {
  1: '建议尺寸960x124',
  2: '建议尺寸230x280',
  3: '建议尺寸1200x90'
}

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  imagePath: [{ required: true, message: '请上传图片', trigger: 'change', }],
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.bdPropaganda_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'bdPropaganda_edit' : 'bdPropaganda_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
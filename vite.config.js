import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { visualizer } from 'rollup-plugin-visualizer'
import { Plugin as importToCDN } from 'vite-plugin-cdn-import'

export default defineConfig({
  plugins: [
    vue(),
    visualizer({ // 打包体积分析
      emitFile: false, // 文件生成到项目路径下，true会生成到dist下
      file: "stats.html", // 分析图生成的文件名
      open: true
    }),
    importToCDN({ // cdn优化
      modules: [
        {
          name: "vue",
          var: "Vue",
          path: "https://cdn.staticfile.org/vue/3.2.45/vue.global.prod.min.js"
        },
        {
          name: "vue-router",
          var: "VueRouter",
          path: "https://cdn.staticfile.org/vue-router/4.1.6/vue-router.global.prod.min.js"
        },
        {
          name: "vuex",
          var: "Vuex",
          path: "https://jiqin-assets.oss-cn-qingdao.aliyuncs.com/vuex/4.1.0/vuex.global.prod.min.js"
        },
        {
          name: "element-plus",
          var: "ElementPlus",
          path: "https://cdn.staticfile.org/element-plus/2.2.30/index.full.min.js"
        },
        // {
        //   name: "ali-oss",
        //   var: "AliOss",
        //   path: "https://cdn.staticfile.org/ali-oss/6.17.1/aliyun-oss-sdk.min.js"
        // },
      ]
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    rollupOptions: {
      input: { // 多页面路口文件设置
        index: fileURLToPath(new URL('./index.html', import.meta.url))
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: "@import '@/styles/color.scss';"
      }
    }
  },
  base: './', // 设置打包路径
  server: {
    port: 4261, // 设置服务启动端口号
    open: true, // 设置服务启动时是否自动打开浏览器
    cors: true, // 允许跨域

    proxy: {
      // '/mallAPI': 'https://applet.jiqinyun.com',
      '/mallAPI': 'http://192.168.2.26:5070',
      //'/mallAPI':'http://admin.mall.jiqinyun.com',
      '/basicAPI': 'https://yun.jiqinyun.com'
    }
  }
})
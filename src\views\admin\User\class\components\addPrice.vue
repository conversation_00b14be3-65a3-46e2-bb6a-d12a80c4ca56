<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="选择商品" prop="name">
        <el-input v-model="form.name" disabled>
          <template #append>
            <el-button :icon="Plus" @click="ProductSelectRef.open()" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="分类定价" prop="unitPrice">
        <el-input v-model="form.unitPrice"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <ProductSelect ref="ProductSelectRef" @success="productSelect" />
</template>

<script setup>
import { ref } from "vue";
import Api from '@/api/user/index.js';
import { Plus } from '@element-plus/icons-vue'
import ProductSelect from '@/views/admin/Product/components/productSelect.vue';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
  customerClassId: null,
  prodSkuId: null,
  prodId: null,
  name: "",
  unitPrice: "",
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  name: [{ required: true, message: '请选择商品', trigger: 'blur', }],
  unitPrice: [{ required: true, message: '请填写定价', trigger: 'blur', }],
}
const open = (customerClassId,id) => {
  form.value.customerClassId = customerClassId
  if (id) {
    title.value = '编辑'
    Api.bdCustomerClassPrice_info({
      infoId: id
    }).then(res => {
      form.value = res.data
      form.value.customerClassId = customerClassId
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = 'bdCustomerClassPrice_save'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

const ProductSelectRef = ref(null)
const productSelect = ({name,prodId,prodSkuId}) => {
  form.value.name = name
  form.value.prodId = prodId
  form.value.prodSkuId = prodSkuId
}

defineExpose({
  open
})
</script>
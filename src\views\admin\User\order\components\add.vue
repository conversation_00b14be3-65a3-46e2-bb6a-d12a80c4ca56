<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="70%"
  >
    <el-divider>基本信息</el-divider>
    <div class="common-info-bg label-list col-3">
      <div class="label-item">
        <div class="label-title">订单号</div>
        <div class="label-content">{{ detail.code }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">客户名称</div>
        <div class="label-content">{{ detail.customerName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">订单状态</div>
        <div class="label-content">{{ detail.orderStateName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">商品金额</div>
        <div class="label-content">{{ detail.totalAmount }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">运费</div>
        <div class="label-content">{{ detail.freightAmount }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">优惠金额</div>
        <div class="label-content">{{ detail.discountAmount }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">实际金额</div>
        <div class="label-content">{{ detail.actualAmount }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">快递状态</div>
        <div class="label-content">{{ detail.expressStateName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">支付状态</div>
        <div class="label-content">{{ detail.payStateName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">支付方式</div>
        <div class="label-content">{{ detail.payTypeName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">收货人</div>
        <div class="label-content">{{ detail.shippingLinkName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">收货手机</div>
        <div class="label-content">{{ detail.shippingPhone }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">收货地址</div>
        <div class="label-content">{{ detail.shippingAddress }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">票据状态</div>
        <div class="label-content">{{ detail.ticketStateName }}</div>
      </div>
      <div class="label-item">
        <div class="label-title">备注</div>
        <div class="label-content">{{ detail.orderRemark }}</div>
      </div>
    </div>
    <el-divider>商品明细</el-divider>
    <CommonTable
      heightType="auto"
      :tableHeader="tableHeader"
      :tableData="detail.prod"
    >
    </CommonTable>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import CommonTable from '@/components/CommonTable.vue';
import Api from '@/api/user/index.js';

const emits = defineEmits(['success'])

const detail = ref({
  prod: []
})
const dialogVisible = ref(false)
const title = ref('订单详情')
const open = (id) => {
  if (id) {
    Api.shoppingOrder_info({
      infoId: id
    }).then(res => {
      detail.value = res.data
    })
  }
  dialogVisible.value = true
}

const close = () => {
  detail.value = {
    prod: []
  }
}

defineExpose({
  open
})

const tableHeader = [
  {
    prop: 'name',
    label: '商品名称',
    width: ''
  },
  {
    prop: 'brand',
    label: '品牌',
    width: ''
  },
  {
    prop: 'deliveryDate',
    label: '货期',
    width: ''
  },
  {
    prop: 'facePrice',
    label: '面价',
    width: ''
  },
  {
    prop: 'model',
    label: '型号',
    width: ''
  },
  {
    prop: 'orderNumber',
    label: '订货号',
    width: ''
  },
  {
    prop: 'unit',
    label: '计量单位',
    width: ''
  },
  {
    prop: 'unitPrice',
    label: '单价',
    width: ''
  },
  {
    prop: 'number',
    label: '数量',
    width: ''
  },
  {
    prop: 'amount',
    label: '金额',
    width: ''
  },
  {
    prop: 'outNum',
    label: '出库数量',
    width: ''
  },
  {
    prop: 'invoiceNum',
    label: '开票数量',
    width: ''
  },
  {
    prop: 'courierNumber',
    label: '发货单号',
    width: ''
  },
]
</script>
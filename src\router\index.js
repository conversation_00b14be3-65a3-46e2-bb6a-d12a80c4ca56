import { createRouter, createWebHashHistory } from 'vue-router'
import Layout from "@/layout/index.vue";
import Login from "@/views/admin/Login/login.vue";

export const routes = [
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/index',
    meta: { title: '首页', icon: 'Odometer', onlyOneLevel: true },
    children: [
      {
        path: '/dashboard/index',
        component: () => import('@/views/admin/Dashboard/index.vue'),
        meta: { title: '概览', type: 'bg_none' }
      },
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/customer',
    meta: { title: '用户', icon: 'Setting' },
    children: [
      {
        path: '/user/customer',
        component: () => import('@/views/admin/User/customer/list.vue'),
        meta: { title: '客户管理', keepAlive: true }
      },
      {
        path: '/user/class',
        component: () => import('@/views/admin/User/class/list.vue'),
        meta: { title: '客户分类', keepAlive: true }
      },
      {
        path: '/user/order',
        component: () => import('@/views/admin/User/order/list.vue'),
        meta: { title: '客户订单', keepAlive: true }
      },
    ]
  },
  {
    path: '/product',
    component: Layout,
    redirect: '/product/class',
    meta: { title: '商品', icon: 'Setting' },
    children: [
      {
        path: '/product/manage',
        component: () => import('@/views/admin/Product/manage/list.vue'),
        meta: { title: '商品管理', keepAlive: true }
      },
      {
        path: '/product/class',
        component: () => import('@/views/admin/Product/class/list.vue'),
        meta: { title: '商品类目', keepAlive: true }
      },
      {
        path: '/product/attribute',
        component: () => import('@/views/admin/Product/attribute/list.vue'),
        meta: { title: '商品规格属性', keepAlive: true }
      },
      {
        path: '/product/tag',
        component: () => import('@/views/admin/Product/tag/list.vue'),
        meta: { title: '商品标签', keepAlive: true }
      },
    ]
  },
  {
    path: '/decorate',
    component: Layout,
    redirect: '/decorate/rotation_img',
    meta: { title: '装修', icon: 'Setting' },
    children: [
      {
        path: '/decorate/rotation_img',
        component: () => import('@/views/admin/Decorate/rotation_img/list.vue'),
        meta: { title: '首页轮播图', keepAlive: true }
      },
      {
        path: '/decorate/propaganda_img',
        component: () => import('@/views/admin/Decorate/propaganda_img/list.vue'),
        meta: { title: '首页宣传图', keepAlive: true }
      },
      {
        path: '/decorate/recommend_product',
        component: () => import('@/views/admin/Decorate/recommend_product/list.vue'),
        meta: { title: '首页推荐产品', keepAlive: true }
      },
      {
        path: '/decorate/news',
        component: () => import('@/views/admin/Decorate/news/list.vue'),
        meta: { title: '资讯管理', keepAlive: true }
      },
      {
        path: '/decorate/footer_menu',
        component: () => import('@/views/admin/Decorate/footer_menu/list.vue'),
        meta: { title: '底部指引', keepAlive: true }
      },
      {
        path: '/decorate/footer_info',
        component: () => import('@/views/admin/Decorate/footer_info/list.vue'),
        meta: { title: '页脚信息', keepAlive: true }
      },
    ]
  },
  {
    path: '/set',
    component: Layout,
    redirect: '/set/base_set',
    meta: { title: '设置', icon: 'Setting' },
    children: [
      {
        path: '/set/base_set',
        component: () => import('@/views/admin/Set/base_set/form.vue'),
        meta: { title: '基础设置', keepAlive: true }
      },
      {
        path: '/set/domain_set',
        component: () => import('@/views/admin/Set/domain_set/list.vue'),
        meta: { title: '域名配置', keepAlive: true }
      },
      {
        path: '/set/contract_template',
        component: () => import('@/views/admin/Set/contract_template/form.vue'),
        meta: { title: '合同模板', keepAlive: true }
      },
    ]
  },
  {
    path: '/',
    component: Login,
    redirect: '/login',
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/login',
    component: Login,
    meta: { title: '登录', hidden: true }
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: routes
})

export default router
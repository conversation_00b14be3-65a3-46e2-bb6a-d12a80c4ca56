<template>
  <div class="fixed-view-container">
    <ViewHeader title="轮播图" add="新增轮播图" @add="addRef.open()" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
      <template #imagePath="{row}">
        <img :src="row.imagePath" style="max-height:120px;" />
      </template>
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
  </div>
</template>

<script>
export default {
  name: '/decorate/rotation_img'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Api from '@/api/decorate/index.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.jqSlideshowimage_list(query)
    return {list:records,total}
  },
  delApi: Api.jqSlideshowimage_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '跳转地址',
    key: 'link'
  },
]

const addRef = ref(null)
const handleData = {
  width: '140',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    slot: 'imagePath',
    label: '轮播图',
    width: ''
  },
  {
    prop: 'link',
    label: '跳转地址',
    width: '300px'
  },
]

</script>
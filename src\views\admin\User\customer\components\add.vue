<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="客户类型">
        <el-radio-group v-model="form.identity">
          <el-radio-button :label="1">个人</el-radio-button>
          <el-radio-button :label="2">企业</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="客户名称" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone"></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="linkName">
        <el-input v-model="form.linkName"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="linkPhone">
        <el-input v-model="form.linkPhone"></el-input>
      </el-form-item>
      <el-form-item label="销售员" prop="saleId">
        <el-select
          v-model="form.saleId"
          style="width: 100%"
        >
          <el-option
            v-for="item in selectData.salesUsers"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="价格策略">
        <el-select
          v-model="form.strategyVal"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in customer_level"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <template v-if="form.identity === 2">
        <el-form-item label="公司名称">
          <el-input v-model="form.companyName"></el-input>
        </el-form-item>
        <el-form-item label="营业执照">
          <ImgUpload v-model="form.businessLicenseImg" :size="2" text="营业执照" />
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";
import ImgUpload from '@/components/ImgUpload.vue';
import Api from '@/api/user/index.js';
import CommonApi from '@/api/common/index.js';
import { PhoneValidator } from "@/utils/global.js";
import { useConstants } from '@/hooks/useConstants.js';

const {customer_level} = useConstants()

const emits = defineEmits(['success'])

const defaultForm = {
  "id": null,
  identity: 1,
	"linkName": "",
	"linkPhone": "",
	"name": "",
	"phone": "",
	"saleId": null,
	"strategyVal": null,
  businessLicenseImg: '',
  companyName: ''
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  name: [{ required: true, message: '请填写空间名称', trigger: 'blur', }],
  linkName: [{ required: true, message: '请填写联系人', trigger: 'blur', }],
  linkPhone: [{ required: true, message: '请填写联系电话', trigger: 'blur', }],
  phone: [
    {
      required: true,
      validator: PhoneValidator,
      trigger: "blur",
    },
  ],
  saleId: [{ required: true, message: '请选择销售员', trigger: 'change', }],
}
const open = (id) => {
  getSelectData()
  if (id) {
    title.value = '编辑'
    Api.customer_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'customer_edit' : 'customer_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

const selectData = reactive({
  salesUsers: [],
})
const getSelectData = () => {
  CommonApi.user_userList({
  }).then(res => {
    selectData.salesUsers = res.data
  })
}

defineExpose({
  open
})
</script>
<template>
  <div class="info-panel">
    <div class="title">
      <span class="line"></span>
      {{title}}
    </div>
    <div class="body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(['title'])
</script>

<style scoped lang="scss">
.info-panel {
  margin-top: 10px;
  border: 1px solid #e6e6e6;
  .title {
    font-size: 12px;
    background-color: #f7f8fa;
    padding: 10px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid #e6e6e6;
    .line {
      display: inline-block;
      width: 3px;
      height: 12px;
      background: $primary;
      margin-right: 5px;
      margin-top: 1px;
      vertical-align: top;
    }
  }
  .body {
    padding: 10px;
  }
}
</style>
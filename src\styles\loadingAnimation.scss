.loading-container {
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,.2);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity .3s;
  &.show{
    visibility: visible;
    opacity: 1;
  }
  &.hide{
    visibility: visible;
    opacity: 0;
  }
  .spinner {
    font-size : 10px;
  }

  .spinner>div {
    background-color: #0083FF;
    height          : 40px;
    width           : 6px;
    display         : inline-block;

    -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    animation        : sk-stretchdelay 1.2s infinite ease-in-out;
  }

  .spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay        : -1.1s;
  }

  .spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    animation-delay        : -1.0s;
  }

  .spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    animation-delay        : -0.9s;
  }

  .spinner .rect5 {
    -webkit-animation-delay: -0.8s;
    animation-delay        : -0.8s;
  }

  @-webkit-keyframes sk-stretchdelay {

    0%,
    40%,
    100% {
      -webkit-transform: scaleY(0.4)
    }

    20% {
      -webkit-transform: scaleY(1.0)
    }
  }

  @keyframes sk-stretchdelay {

    0%,
    40%,
    100% {
      transform        : scaleY(0.4);
      -webkit-transform: scaleY(0.4);
    }

    20% {
      transform        : scaleY(1.0);
      -webkit-transform: scaleY(1.0);
    }
  }
}
<template>
  <el-breadcrumb separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="item in breadList" :key="item.path" :to="item.path">{{item.title}}</el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
  import {computed} from 'vue'
  import {useRoute} from 'vue-router'

  const route = useRoute()
  const breadList = computed(() => {
    let breads = route.matched.filter(v => v.meta && v.meta.title).map(item => {
      return {
        path: item.path,
        title: item.meta.title
      }
    })
    return breads
  })

</script>
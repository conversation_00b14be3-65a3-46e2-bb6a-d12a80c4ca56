import API from "@/api/common/index.js";

const state = {
  constant: {},
};

const getters = {
  constant: (state) => state.constant,
};

const mutations = {
  SET_CONSTANT(state, obj) {
    state.constant = obj;
  },
};

const actions = {
  getConstant({ commit }, params) {
    return new Promise((resolve, reject) => {
      API.dict_all(params)
        .then((res) => {
          commit("SET_CONSTANT", res.data);
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};

export default {
  state,
  getters,
  mutations,
  actions,
};

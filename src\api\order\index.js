import Axios from '../axios'
import env from '../environment'
export default {

  // 计算褶数与米数
  pleatNumberComputer: params => Axios.post(`${env.API}busniessOrder/pleatNumberComputer`, params),

  // 计算器
  calculator: params => Axios.post(`${env.API}busniessOrder/calculator`, params),

  // 保存
  save: params => Axios.post(`${env.API}busniessOrder/save`, params),
  list: params => Axios.post(`${env.API}busniessOrder/list`, params),
  del: params => Axios.post(`${env.API}busniessOrder/del`, params),
  info: params => Axios.post(`${env.API}busniessOrder/info`, params),

  // 按类别打印列表
  infoGroupByCategory: params => Axios.post(`${env.API}busniessOrder/infoGroupByCategory`, params),

  // 发货
  orderProductList: params => Axios.post(`${env.API}busniessOrder/orderSpaceOutList`, params),
  inOut: params => Axios.post(`${env.API}busniessOrder/inOutSpace`, params),

  // 收款
  busniessOrder_transferInGathering: params => Axios.post(`${env.API}busniessOrder/transferInGathering`, params),

  // 收款单
  busniessOrderGathering_list: params => Axios.post(`${env.API}busniessOrderGathering/list`, params),
  busniessOrderGathering_save: params => Axios.post(`${env.API}busniessOrderGathering/save`, params),
  busniessOrderGathering_info: params => Axios.post(`${env.API}busniessOrderGathering/info`, params),
  busniessOrderGathering_del: params => Axios.post(`${env.API}busniessOrderGathering/del`, params),

  // 提现申请
  customerWithdrawal_list: params => Axios.post(`${env.API}customerWithdrawal/list`, params),
  customerWithdrawal_withdrawalConfirmationOrReturn: params => Axios.post(`${env.API}customerWithdrawal/withdrawalConfirmationOrReturn`, params),

  // 单位
  busniessUnitSetting_pullDown: params => Axios.post(`${env.API}busniessUnitSetting/pullDown`, params),
}
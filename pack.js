import dayjs from 'dayjs';
import child_process from 'child_process';

function exec (cmd) {
  return child_process.execSync(cmd).toString().trim()
}

let _date = dayjs().format('YYYY-MM-DD-HH-mm-ss')
let name = '商城后台v2'
let _file = `${name}_${_date}.tar.gz`
exec('tar czvf ' + _file + ' dist')

const uploadMenu = `root@47.104.250.140:/root/front_end/mall/admin/`

setTimeout(() => {
  console.log(`
    压缩完成：${_file}

    上传命令：
      scp -P 22 ${_file} ${uploadMenu}
    
    解压命令：
      tar -xvf ${_file} -C ./
`)
}, 200)

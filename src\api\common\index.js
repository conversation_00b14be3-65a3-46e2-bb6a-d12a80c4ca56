import Axios from '../axios'
import env from '../environment'
export default {

  // 常量
  dict_all: params => Axios.post(`${env.API}sysDict/typeList`, params),

  // 中国行政区域数据
  cnarea_dataByParentCode: params => Axios.post(`${env.API}cnarea/dataByParentCode`, params),

  // 用户身份列表
  user_userList: params => Axios.post(`${env.API}admin/user/userList`, params),

  // 首页统计数据
  index_statisticalData: params => Axios.post(`${env.API}admin/index/statisticalData`, params),
  
}
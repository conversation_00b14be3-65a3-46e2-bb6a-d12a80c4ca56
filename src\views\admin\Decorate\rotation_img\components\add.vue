<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="图片" prop="imagePath">
        <ImgUpload
          v-model="form.imagePath"
          :size="2"
          text="建议尺寸1920x460"
        />
      </el-form-item>
      <el-form-item label="跳转地址">
        <el-input v-model="form.link"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import ImgUpload from '@/components/ImgUpload.vue';
import Api from '@/api/decorate/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
	"imagePath": "",
	"link": ""
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  imagePath: [{ required: true, message: '请上传图片', trigger: 'change', }],
}
const open = (id) => {
  if (id) {
    title.value = '编辑'
    Api.jqSlideshowimage_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'jqSlideshowimage_edit' : 'jqSlideshowimage_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
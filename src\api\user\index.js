import Axios from '../axios'
import env from '../environment'
export default {

  // 客户
  customer_add: params => Axios.post(`${env.API}admin/customer/add`, params),
  customer_del: params => Axios.post(`${env.API}admin/customer/del`, params),
  customer_edit: params => Axios.post(`${env.API}admin/customer/edit`, params),
  customer_info: params => Axios.post(`${env.API}admin/customer/info`, params),
  customer_list: params => Axios.post(`${env.API}admin/customer/list`, params),

  // 客户分类
  bdCustomerClass_add: params => Axios.post(`${env.API}admin/bdCustomerClass/add`, params),
  bdCustomerClass_del: params => Axios.post(`${env.API}admin/bdCustomerClass/del`, params),
  bdCustomerClass_edit: params => Axios.post(`${env.API}admin/bdCustomerClass/edit`, params),
  bdCustomerClass_info: params => Axios.post(`${env.API}admin/bdCustomerClass/info`, params),
  bdCustomerClass_list: params => Axios.post(`${env.API}admin/bdCustomerClass/list`, params),

  // 客户分类产品定价
  bdCustomerClassPrice_save: params => Axios.post(`${env.API}admin/bdCustomerClassPrice/save`, params),
  bdCustomerClassPrice_info: params => Axios.post(`${env.API}admin/bdCustomerClassPrice/info`, params),
  bdCustomerClassPrice_priceList: params => Axios.post(`${env.API}admin/bdCustomerClassPrice/priceList`, params),
  bdCustomerClassPrice_batchDel: params => Axios.post(`${env.API}admin/bdCustomerClassPrice/batchDel`, params),

  // 订单
  shoppingOrder_list: params => Axios.post(`${env.API}admin/shoppingOrder/list`, params),
  shoppingOrder_info: params => Axios.post(`${env.API}admin/shoppingOrder/info`, params),
  shoppingOrder_del: params => Axios.post(`${env.API}admin/shoppingOrder/del`, params),
  shoppingOrder_cancelOrder: params => Axios.post(`${env.API}admin/shoppingOrder/cancelOrder`, params),
  shoppingOrder_updateOrder: params => Axios.post(`${env.API}admin/shoppingOrder/updateOrder`, params),
  
}
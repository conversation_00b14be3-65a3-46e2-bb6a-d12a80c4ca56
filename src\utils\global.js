import { ElMessageBox } from 'element-plus';

export const Confirm = (callBack = ()=>{}, message = '确认删除这条数据?') => {
  ElMessageBox.confirm(message, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    callBack()
  }).catch(() => {})
}

export const PhoneValidator = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('手机号不能为空'))
  } else if (!/^1\d{10}$/.test(value)) {
    callback(new Error('手机号格式错误'))
  }
  callback()
}

/**
 * 从对象中获取特定字段组成的新对象，没有值返回null
 * @param {*} obj 对象  arr 具体字段字符串 如需转换字段用/隔开，取后面的字段作为新key
 * @return Object
 */
export const GetFieldFromObject = (obj={},arr=[]) => {
  let newObj = {}
  for (let key in obj) {
    let field = arr.find(item => item.startsWith(key))
    if (!field) continue;
    
    if (field.includes('/')) { // 转换
      let [oldField,newField] = field.split('/')
      newObj[newField] = obj[key] ?? null
    } else {
      newObj[key] = obj[key] ?? null
    }
  }
  return newObj
}

/**
 * 延迟触发防抖函数
 * @param {*} func 回调
 * @param {*} wait 延迟时间
 * @returns function
 */
 export const Debounce = (func, wait = 1000) => {
  let timeout;
  return function () {
    const context = this;
    const args = [...arguments];
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args)
    }, wait);
  }
}

// 深拷贝
export const DeepClone = (obj) => {
	let newObj = Array.isArray(obj) ? [] : {}
	if(typeof obj !== 'object' || obj === null) {
		return obj
	}else{
		for(var i in obj) {
			if(typeof obj[i] === 'object'){
				newObj[i] = DeepClone(obj[i])
			}else{
				newObj[i] = obj[i]
			}
		}
	}
	return newObj;
}
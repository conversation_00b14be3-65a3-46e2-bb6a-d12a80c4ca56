export class Loading {
  loadingDom
  count = 0 // 计数器

  constructor() {
    let bodyDom = document.getElementsByTagName('body')[0]
    let loadingDom = document.getElementsByClassName('loading-container')?.[0]

    if (!loadingDom) {
      loadingDom = document.createElement('div')
      loadingDom.classList.add('loading-container')
      loadingDom.innerHTML = `
        <div class="spinner">
          <div class="rect1"></div>
          <div class="rect2"></div>
          <div class="rect3"></div>
          <div class="rect4"></div>
          <div class="rect5"></div>
        </div>
      `
    }

    this.loadingDom = loadingDom
    bodyDom.appendChild(this.loadingDom)
  }

  show() {
    this.count++
    let height = this.loadingDom.offsetHeight // 触发重绘
    this.loadingDom.classList.add('show')
  }

  hide() {
    if (this.count <= 0) {
      this.count = 0
    } else{ this.count-- }
    if (this.count === 0) {
      this.loadingDom.classList.remove('show')
      this.loadingDom.classList.add('hide')
      setTimeout(() => {
        this.loadingDom.classList.remove('hide')
      }, 300)
    }
  }
}
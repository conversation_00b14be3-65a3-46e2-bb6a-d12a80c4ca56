<template>
  <div class="header-container flex-bc">
    <div>
      <Crumbs />
    </div>
    <el-dropdown class="h100">
      <div class="user flex-cc">
        <img src="https://www.lingchen.kim/tao_admin/static/img/avatar.0e13e70.png" />
        <span class="ml10 mr5 c666">{{ userInfo.companyName || userInfo.name }}</span>
        <el-icon><CaretBottom /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <!-- <el-dropdown-item @click="showUserInfo">个人信息</el-dropdown-item> -->
          <el-dropdown-item
            divided
            @click="outLogin"
          >退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import Crumbs from './Crumbs.vue'
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { CaretBottom } from '@element-plus/icons-vue'

const router = useRouter()
const store = useStore()
const userInfo = store.getters.userInfo
const showUserInfo = () => {
  if (userInfo.offlineSuppliers && [0, 1,2].includes(userInfo.offlineSuppliers)) {
    //供应商
    router.replace('/set/baseinfo_set')
  } else {
    //平台
    router.replace('/set/baseinfo_set')
  }
}

const outLogin =()=>{
router.replace('/login')
}

computed
</script>

<style scoped lang="scss">
.header-container {
  padding: 0 15px;
  height: 60px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}
.user {
  height: 100%;
  padding: 0 10px;
  transition: all 0.3s;
  cursor: pointer;
  &:hover {
    background-color: #eee;
  }
  img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
}
</style>
<template>
  <!-- 有多个子路由 -->
  <el-submenu :index="nodeData.path" v-if="hasManyChild(nodeData) && isShow">
    <template #title>
      <i :class="nodeData.meta.icon"></i>
      <span>{{ nodeData.meta.title }}</span>
    </template>
    <MenuItem v-for="item in nodeData.children" :key="item.name" :nodeData="item" :basePath="resolvePath(item.path)" />
  </el-submenu>
  <!-- 只有一个子路由 -->
  <template v-else-if="onlyOneChild && isShow">
    <MenuItem :nodeData="onlyOneChild" :basePath="resolvePath(onlyOneChild.path)" />
  </template>
  <!-- 最末级路由 -->
  <el-menu-item :index="basePath" v-else-if="isShow">
    <i :class="nodeData.meta.icon" v-if="nodeData.meta.icon"></i>
    <template #title>{{ nodeData.meta.title }}</template>
  </el-menu-item>
</template>

<script>
import path from 'path-browserify'
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'MenuItem',
  props: {
    nodeData: {
      type: Object,
      default: () => { return {} }
    },
    basePath: {
      type: String
    }
  },
  computed: {
    isShow() {
      return !this.nodeData.meta.hidden
    },
    onlyOneChild() {
      if (this.nodeData.children && this.nodeData.children.length === 1) return this.nodeData.children[0]
    }
  },
  methods: {
    resolvePath(routePath) {
      return path.resolve(this.basePath, routePath)
    },
    hasManyChild(item) {
      if (!item.children || item.children.length < 2) {
        return false
      }
      return true
    }
  }
})
</script>
<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="优惠金额" prop="discountAmount">
        <el-input v-model="form.discountAmount"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import Api from '@/api/user/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
  discountAmount: "",
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('修改优惠金额')
const rules = {
  discountAmount: [{ required: true, message: '请填写优惠金额', trigger: 'blur', }],
}
const open = (id) => {
  if (id) {
    Api.shoppingOrder_info({
      infoId: id
    }).then(res => {
      let {id: resId, discountAmount} = res.data
      form.value.id = resId
      form.value.discountAmount = discountAmount
    })
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      Api.shoppingOrder_updateOrder(form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
}

defineExpose({
  open
})
</script>
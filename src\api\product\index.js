import Axios from '../axios'
import env from '../environment'
export default {

  // 类目
  bdCategory_add: params => Axios.post(`${env.API}admin/bdCategory/add`, params),
  bdCategory_del: params => Axios.post(`${env.API}admin/bdCategory/del`, params),
  bdCategory_edit: params => Axios.post(`${env.API}admin/bdCategory/edit`, params),
  bdCategory_info: params => Axios.post(`${env.API}admin/bdCategory/info`, params),
  bdCategory_treeList: params => Axios.post(`${env.API}admin/bdCategory/treeList`, params),
  bdCategory_categoryProdPopList: params => Axios.post(`${env.API}admin/bdCategory/categoryProdPopList`, params),

  // 规格
  bdProdPop_add: params => Axios.post(`${env.API}admin/bdProdPop/add`, params),
  bdProdPop_del: params => Axios.post(`${env.API}admin/bdProdPop/del`, params),
  bdProdPop_edit: params => Axios.post(`${env.API}admin/bdProdPop/edit`, params),
  bdProdPop_info: params => Axios.post(`${env.API}admin/bdProdPop/info`, params),
  bdProdPop_list: params => Axios.post(`${env.API}admin/bdProdPop/list`, params),
  bdProdPop_batchInfo: params => Axios.post(`${env.API}admin/bdProdPop/batchInfo`, params),

  // 商品管理
  bdProd_add: params => Axios.post(`${env.API}admin/bdProd/add`, params),
  bdProd_del: params => Axios.post(`${env.API}admin/bdProd/del`, params),
  bdProd_edit: params => Axios.post(`${env.API}admin/bdProd/edit`, params),
  bdProd_info: params => Axios.post(`${env.API}admin/bdProd/info`, params),
  bdProd_list: params => Axios.post(`${env.API}admin/bdProd/list`, params),
  bdProd_updateState: params => Axios.post(`${env.API}admin/bdProd/updateState`, params),
  bdProd_batchUpdateProdPop: params => Axios.post(`${env.API}admin/bdProd/batchUpdateProdPop`, params),

  // 商品标签
  bdProdTag_add: params => Axios.post(`${env.API}admin/bdProdTag/add`, params),
  bdProdTag_del: params => Axios.post(`${env.API}admin/bdProdTag/del`, params),
  bdProdTag_edit: params => Axios.post(`${env.API}admin/bdProdTag/edit`, params),
  bdProdTag_info: params => Axios.post(`${env.API}admin/bdProdTag/info`, params),
  bdProdTag_list: params => Axios.post(`${env.API}admin/bdProdTag/list`, params),
  bdProdTag_infoProd: params => Axios.post(`${env.API}admin/bdProdTag/infoProd`, params),
  bdProdTag_addProd: params => Axios.post(`${env.API}admin/bdProdTag/addProd`, params),
  bdProdTag_delProd: params => Axios.post(`${env.API}admin/bdProdTag/delProd`, params),

  // 价格策略
  bdProdStrategyPrice_edit: params => Axios.post(`${env.API}admin/bdProdStrategyPrice/edit`, params),
  bdProdStrategyPrice_info: params => Axios.post(`${env.API}admin/bdProdStrategyPrice/info`, params),

  // 数量价格
  bdProdNumPrice_add: params => Axios.post(`${env.API}admin/bdProdNumPrice/add`, params),
  bdProdNumPrice_edit: params => Axios.post(`${env.API}admin/bdProdNumPrice/edit`, params),
  bdProdNumPrice_info: params => Axios.post(`${env.API}admin/bdProdNumPrice/info`, params),
  bdProdNumPrice_del: params => Axios.post(`${env.API}admin/bdProdNumPrice/del`, params),
  bdProdNumPrice_list: params => Axios.post(`${env.API}admin/bdProdNumPrice/list`, params),

}
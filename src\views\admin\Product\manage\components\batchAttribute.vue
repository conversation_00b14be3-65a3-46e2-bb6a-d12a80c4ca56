<template>
  <el-dialog :title="title" v-model="dialogVisible" @close="close" :close-on-click-modal="false"
    :close-on-press-escape="false">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="商品类目" prop="categoryId">
        <el-cascader
          v-model="form.categoryArray"
          :props="{
            value: 'id',
            label: 'name',
            checkStrictly: true
          }"
          :options="selectData.categoryTree"
          @change="cascaderChange"
          style="width: 100%;"
        />
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="addRow">新增一行</el-button>
    <CommonTable class="mt10" :tableHeader="tableHeader" :tableData="form.properties" :handleData="handleData">
      <template #name="{ row, $index }">
        <el-select v-model="row.id" style="width: 100%" @change="val => prodPopChange(val, row)">
          <el-option v-for="item in selectData.prodPopList" :key="item.id" :label="item.name"
            :value="item.id"></el-option>
        </el-select>
      </template>
      <template #value="{ row }">
        <el-select v-model="row.value" filterable style="width: 100%">
          <el-option v-for="item in row.valueList" :key="item.id" :label="item.value" :value="item.id"></el-option>
        </el-select>
      </template>
    </CommonTable>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, toRaw } from "vue";
import CommonTable from '@/components/CommonTable.vue';
import Api from '@/api/product/index.js';
import { DeepClone } from '@/utils/global.js'

const defaultProperties = {
  "id": null,
  "name": "",
  "value": null,
  valueList: []
}
const defaultForm = {
  categoryId: null,
  categoryArray: [],
  "ids": [],
  "properties": [
    DeepClone(defaultProperties)
  ]
}
const form = ref(defaultForm)
const addRow = () => {
  form.value.properties.push(DeepClone(defaultProperties))
}
const cascaderChange = value => {
  form.value.categoryId = toRaw(value).at(-1)
}

const dialogVisible = ref(false)
const title = ref('批量修改规格属性')
const rules = {
  categoryId: [{ required: true, message: '请选择商品类目', trigger: 'change', }],
}
const open = (ids = []) => {
  form.value.ids = ids
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      Api.bdProd_batchUpdateProdPop(form.value).then(res => {
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
}

const selectData = reactive({
  categoryTree: [],
  prodPopList: [],
})
const getSelectData = () => {
  Api.bdCategory_treeList().then(res => {
    selectData.categoryTree = res.data
  })
  Api.bdProdPop_list({
    pageNum: 1,
    pageSize: 1000,
  }).then(res => {
    selectData.prodPopList = res.records
  })
}
onMounted(() => {
  getSelectData()
})

const tableHeader = [
  {
    slot: 'name',
    label: '选择规格',
    width: ''
  },
  {
    slot: 'value',
    label: '规格值',
    width: ''
  }
]

const handleData = {
  width: '100',
  handle: [
    {
      label: '删除',
      fun: (row, index) => {
        form.value.properties.splice(index, 1)
      }
    },
  ]
}

const prodPopChange = (val, row) => {
  Api.bdProdPop_info({
    infoId: val
  }).then(res => {
    row.valueList = res.data?.prodPropValues ?? []
    row.value = null
  })
}

defineExpose({
  open
})
</script>
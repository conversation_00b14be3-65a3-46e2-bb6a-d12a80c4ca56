<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="数量" prop="number">
        <el-input v-model="form.number"></el-input>
      </el-form-item>
      <el-form-item label="单价" prop="unitPrice">
        <el-input v-model="form.unitPrice"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="add">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import Api from '@/api/product/index.js';

const emits = defineEmits(['success'])

const defaultForm = {
  id: null,
  prodId: null,
	"number": 0,
	"unitPrice": 0
}
const form = ref(defaultForm)

const dialogVisible = ref(false)
const title = ref('新增')
const rules = {
  number: [{ required: true, message: '请输入数量', trigger: 'blur', }],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur', }],
}
const open = (id, prodId) => {
  if (id) {
    title.value = '编辑'
    Api.bdProdNumPrice_info({
      infoId: id
    }).then(res => {
      form.value = res.data
    })
  } else {
    form.value.prodId = prodId
  }
  dialogVisible.value = true
}

const formRef = ref(null)
const add = () => {
  formRef.value.validate(valid => {
    if (valid) {
      let url = form.value.id ? 'bdProdNumPrice_edit' : 'bdProdNumPrice_add'
      Api[url](form.value).then(res => {
        emits('success')
        dialogVisible.value = false
      })
    }
  })
}
const close = () => {
  Object.assign(form.value, defaultForm)
  formRef.value.resetFields()
  title.value = '新增'
}

defineExpose({
  open
})
</script>
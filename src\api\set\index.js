import Axios from '../axios'
import env from '../environment'
export default {

  // 系统设置
  companyInfo_info: params => Axios.post(`${env.API}companyInfo/info`, params),
  companyInfo_save: params => Axios.post(`${env.API}companyInfo/save`, params),

  // 域名
  bdDomainname_add: params => Axios.post(`${env.API}admin/bdDomainname/add`, params),
  bdDomainname_del: params => Axios.post(`${env.API}admin/bdDomainname/del`, params),
  bdDomainname_edit: params => Axios.post(`${env.API}admin/bdDomainname/edit`, params),
  bdDomainname_info: params => Axios.post(`${env.API}admin/bdDomainname/info`, params),
  bdDomainname_list: params => Axios.post(`${env.API}admin/bdDomainname/list`, params),

  // 合同模板
  bdCompact_info: params => Axios.post(`${env.API}admin/bdCompact/info`, params),
  bdCompact_save: params => Axios.post(`${env.API}admin/bdCompact/save`, params),
  
}
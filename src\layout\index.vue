<template>
  <div class="app-container">
    <div class="app-asider">
      <Asider />
    </div>
    <div class="app-main">
      <div class="app-header">
        <Header />
      </div>
      <div class="app-header">
        <NavBar />
      </div>
      <div class="app-router-container">
        <Content />
      </div>
    </div>
  </div>
</template>

<script setup>
import Asider from './components/Asider.vue'
import Header from './components/Header.vue'
import NavBar from './components/NavBar.vue'
import Content from './components/Content.vue'
</script>

<style scoped lang="scss">
.app-container {
  width: 100vw;
  height: 100vh;
  font-size: 14px;
  display: flex;
  overflow: hidden;
}
.app-asider {
  flex: 0 0 100px;
  height: 100%;
  background-color: #191a23;
}
.app-main{
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f6f7f9;
  overflow: hidden;
}
.app-header{
  flex: 0 0 auto;
}
.app-router-container{
  flex: 1;
  padding: 0 15px 15px 15px;
  background-color: $bg-color-light;
  overflow: hidden;
}
</style>
<template>
  <el-dialog
    width="800px"
    title="选择商品"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <FormSearch
      hideLabel
      :span="12"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      heightType="fixed"
      height="400px"
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <!-- <el-button type="primary" @click="add">确 定</el-button> -->
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, toRaw } from "vue";
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Api from '@/api/product/index.js';

const { tableData, pageParams, searchParams, getList } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdProd_list(query)
    return {list:records,total}
  },
  delApi: Api.bdProd_del
})

const formData = [
  {
    type: 'input',
    label: '商品名称',
    key: 'name'
  },
]

const dialogVisible = ref(false)
const open = () => {
  getList()
  dialogVisible.value = true
}
const close = () => {
  
}

defineExpose({
  open
})

const emits = defineEmits(['success'])

const handleData = {
  width: '100',
  handle: [
    {
      label: '选择',
      fun: (row) => {
        emits('success', toRaw(row))
        dialogVisible.value = false
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'name',
    label: '商品名称',
    width: ''
  },
  {
    prop: 'brand',
    label: '品牌',
    width: ''
  },
  {
    prop: 'deliveryDate',
    label: '货期',
    width: ''
  },
  {
    prop: 'facePrice',
    label: '面价',
    width: ''
  },
  {
    prop: 'model',
    label: '型号',
    width: ''
  },
  {
    prop: 'orderNumber',
    label: '订货号',
    width: ''
  },
  {
    prop: 'unit',
    label: '计量单位',
    width: ''
  },
  {
    prop: 'minBuyNum',
    label: '最小起购量',
    width: ''
  },
  {
    prop: 'status',
    label: '是否上架',
    width: ''
  },
]
</script>
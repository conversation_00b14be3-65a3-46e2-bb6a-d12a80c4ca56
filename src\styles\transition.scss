// router transition
.router-transform-enter-active, .router-transform-leave-active {
  transition: all .5s ease;
}
.router-transform-enter, .router-transform-leave-to {
  opacity: 0;
}
.router-transform-enter-to, .router-transform-leave {
  opacity: 1;
}

// outer transition test
.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all 0.3s ease-out;
}

.fade-transform-enter-from {
    opacity: 0;
    transform: translateX(-30px);
}

.fade-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

// breadcrumb transition
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}
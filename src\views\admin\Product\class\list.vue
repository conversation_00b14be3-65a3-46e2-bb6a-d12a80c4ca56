<template>
  <div class="fixed-view-container">
    <ViewHeader title="商品类目" add="新增一级类目" @add="addRef.open()" />
    <div class="table-container mt10">
      <el-table
        :data="tableData"
        height="100%"
        row-key="id"
        border
      >
        <el-table-column prop="name" label="类目名称" />
        <el-table-column align="center" fixed="right" label="操作" width="240px">
          <template #default="{ row }">
            <el-button text bg type="primary" size="small" @click="addRef.open(row.id)">编辑</el-button>
            <el-button text bg type="primary" size="small" @click="addRef.open(row.id,'addChild')">新增子类目</el-button>
            <el-button text bg type="danger" size="small" @click="del(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Add ref="addRef" @success="getList" />
  </div>
</template>

<script>
export default {
  name: '/product/class'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import Add from "./components/add.vue";
import Api from '@/api/product/index.js';
import { Confirm } from '@/utils/global.js';

const tableData = ref([])
const getList = () => {
  Api.bdCategory_treeList().then(res => {
    tableData.value = res.data
  })
}

onMounted(() => {
  getList()
})

const addRef = ref(null)

const del = (id) => {
  Confirm(() => {
    Api.bdCategory_del({id}).then(res => {
      getList()
    })
  })
}

</script>
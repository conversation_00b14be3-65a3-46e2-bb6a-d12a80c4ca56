<template>
  <el-dialog
    width="900px"
    :title="title"
    v-model="dialogVisible"
    @close="close"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-button type="primary" size="small" @click="numberRef.open(null,searchParams.prodId)">新增数量价格</el-button>
    <CommonTable
      class="mt10"
      heightType="fixed"
      height="400px"
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
  <Number ref="numberRef" @success="getList" />
</template>

<script setup>
import { ref } from "vue";
import CommonTable from '@/components/CommonTable.vue';
import Pagination from "@/components/Pagination.vue";
import Number from "./number.vue";
import Api from '@/api/product/index.js';
import { useList } from "@/hooks/useCommonTable.js";

const { tableData, pageParams, searchParams, getList, delItem, listReset } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdProdNumPrice_list(query)
    return {list:records,total}
  },
  delApi: Api.bdProdNumPrice_del
})

const dialogVisible = ref(false)
const title = ref('数量价格列表')
const open = (id) => {
  if (id) {
    searchParams.value.prodId = id
    getList()
  }
  dialogVisible.value = true
}

const close = () => {
  listReset()
}

defineExpose({
  open
})

const numberRef = ref(null)
const handleData = {
  width: '120',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        numberRef.value.open(row.id, searchParams.value.prodId)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'number',
    label: '数量',
    width: ''
  },
  {
    prop: 'unitPrice',
    label: '单价',
    width: ''
  },
]
</script>
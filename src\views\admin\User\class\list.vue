<template>
  <div class="fixed-view-container">
    <ViewHeader title="客户分类" add="新增客户分类" @add="addRef.open()" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
    <Product ref="ProductRef" />
  </div>
</template>

<script>
export default {
  name: '/user/class'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Product from "./components/product.vue";
import Api from '@/api/user/index.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.bdCustomerClass_list(query)
    return {list:records,total}
  },
  delApi: Api.bdCustomerClass_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '客户分类名称',
    key: 'name'
  },
]

const addRef = ref(null)
const ProductRef = ref(null)
const handleData = {
  width: '220',
  handle: [
    {
      label: '编辑',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '分类定价',
      fun: (row) => {
        ProductRef.value.open(row.id)
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'name',
    label: '客户分类名称',
    width: ''
  },
  {
    prop: 'updateTime',
    label: '上次修改时间',
    width: ''
  },
]

</script>
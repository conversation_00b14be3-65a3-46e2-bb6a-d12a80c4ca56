import Axios from '../axios'
import env from '../environment'
export default {

  // 经营情况
  basic_businessSituation: params => Axios.post(`${env.API}basic/businessSituation`, params),

  // 客户排名
  basic_customerRanking: params => Axios.post(`${env.API}basic/customerRanking`, params),

  // 独立数据统计
  basic_independentDataStatistics: params => Axios.post(`${env.API}basic/independentDataStatistics`, params),

  // 产品统计
  basic_curtainSaleRanking: params => Axios.post(`${env.API}basic/curtainSaleRanking`, params),

  // 产品统计
  report_curtainCustomerSale: params => Axios.post(`${env.API}report/curtainCustomerSale`, params),

  // 小程序申请记录
  busniessCustomerApplication_list: params => Axios.post(`${env.API}busniessCustomerApplication/list`, params),
  
}
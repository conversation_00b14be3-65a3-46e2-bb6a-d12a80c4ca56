<template>
  <div class="fixed-view-container">
    <ViewHeader title="客户订单" />
    <FormSearch
      class="mt20"
      :formData="formData"
      :searchParams="searchParams"
      @search="getList"
    />
    <CommonTable
      :tableHeader="tableHeader"
      :tableData="tableData"
      :handleData="handleData"
    >
    </CommonTable>
    <Pagination :pageParams="pageParams" @handle="getList" />
    <Add ref="addRef" @success="getList" />
    <Preferential ref="preferentialRef" @success="getList" />
  </div>
</template>

<script>
export default {
  name: '/user/order'
}
</script>
<script setup>
import { ref, onMounted } from 'vue';
import ViewHeader from '@/components/ViewHeader.vue';
import CommonTable from '@/components/CommonTable.vue';
import FormSearch from "@/components/FormSearch.vue";
import Pagination from "@/components/Pagination.vue";
import { useList } from "@/hooks/useCommonTable.js";
import Add from "./components/add.vue";
import Preferential from "./components/preferential.vue";
import Api from '@/api/user/index.js';
import { Confirm } from '@/utils/global.js';

const { tableData, pageParams, searchParams, getList, delItem } = useList({
  getListApi: async (query) => {
    const {records,total} = await Api.shoppingOrder_list(query)
    return {list:records,total}
  },
  delApi: Api.shoppingOrder_del
})
onMounted(() => {
  getList()
})

const formData = [
  {
    type: 'input',
    label: '客户名称',
    key: 'customerName'
  },
]

const addRef = ref(null)
const preferentialRef = ref(null)
const handleData = {
  width: '260',
  handle: [
    {
      label: '详情',
      fun: (row) => {
        addRef.value.open(row.id)
      }
    },
    {
      label: '优惠金额',
      fun: (row) => {
        preferentialRef.value.open(row.id)
      }
    },
    {
      label: '取消',
      fun: (row) => {
        Confirm(() => {
          Api.shoppingOrder_cancelOrder({
            id: row.id
          }).then(res => {
            getList()
          })
        }, '是否取消该订单？')
      }
    },
    {
      label: '删除',
      fun: (row) => {
        delItem(row.id)
      }
    }
  ]
}

const tableHeader = [
  {
    prop: 'code',
    label: '订单号',
    width: ''
  },
  {
    prop: 'customerName',
    label: '客户名称',
    width: ''
  },
  {
    prop: 'orderStateName',
    label: '订单状态',
    width: ''
  },
  {
    prop: 'totalAmount',
    label: '商品金额',
    width: ''
  },
  {
    prop: 'freightAmount',
    label: '运费',
    width: ''
  },
  {
    prop: 'discountAmount',
    label: '优惠金额',
    width: ''
  },
  {
    prop: 'actualAmount',
    label: '实际金额',
    width: ''
  },
  {
    prop: 'expressStateName',
    label: '快递状态',
    width: ''
  },
  {
    prop: 'payStateName',
    label: '支付状态',
    width: ''
  },
  {
    prop: 'payTypeName',
    label: '支付方式',
    width: ''
  },
  {
    prop: 'orderRemark',
    label: '备注',
    width: ''
  },
]

</script>
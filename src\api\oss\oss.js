import OSS from "ali-oss";
import { ElMessage } from "element-plus";
import _dayjs from "dayjs";
import config from './config'

function randomString (len = 32) {
  let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  let maxPos = chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}

export default {
  // 上传单个文件
  uploadFile: (file,systemPath = null) => {
    //如果有传系统路径就用那个路径层级，没用的话默认用时间
    let path = systemPath ? systemPath : _dayjs().format("YYYY_MM_DD");
    let name = `${path}/${randomString(8)}_${file.size}_${file.name}`;
    let client = new OSS({
      region: config.region,
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
      bucket: config.bucket
    });

    let res = null;
    let uploadPut = async function uploadPut(client, name, file) {
      try {
        res = await client.put(`${name}`, file);
        if (!res.url) {
          ElMessage({
            showClose: true,
            message: `十分抱歉，上传文件失败，请联系管理员解决`,
            type: "error"
          });
          return Promise.reject(res);
        }
        //把http改成https
        let flag = res.url.indexOf("https");
        if (flag === -1) {
          res.url = res.url.replace(/^http/, "https");
        }
        return res;
      } catch (e) {
        ElMessage({
          showClose: true,
          message: `十分抱歉，上传文件失败，请联系管理员解决`,
          type: "error"
        });
        return Promise.reject(e.toString());
      }
    };
    return uploadPut(client, name, file);
  }
};
